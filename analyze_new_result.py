#!/usr/bin/env python3
import pandas as pd

# 读取新的结果文件
result_file = '监考安排_20250602_160104.xlsx'

print("=== 分析新结果文件中的缺1情况 ===")

# 1. 查看考场安排表中的缺1情况
print("1. 考场安排表中的缺1情况:")
room_result = pd.read_excel(result_file, sheet_name='考场安排')

missing_cases = []
for index, row in room_result.iterrows():
    room_name = row['考场']
    for subject in ['语文', '数学', '英语', '理化', '政史']:
        teachers = str(row[subject])
        if '缺1' in teachers:
            missing_cases.append({
                '考场': room_name,
                '科目': subject,
                '详情': teachers
            })

if missing_cases:
    print("发现的缺1情况:")
    for case in missing_cases:
        print(f"  {case['考场']}-{case['科目']}: {case['详情']}")
else:
    print("  没有发现任何缺1的情况")

# 2. 查看未安排场次表
print("\n2. 未安排场次表:")
try:
    unassigned = pd.read_excel(result_file, sheet_name='未安排场次')
    if unassigned.empty:
        print("  未安排场次表为空")
    else:
        print("  未安排场次详情:")
        print(unassigned.to_string(index=False))
except Exception as e:
    print(f"  读取未安排场次表时出错: {e}")

# 3. 统计分析
print("\n3. 统计分析:")
try:
    stats = pd.read_excel(result_file, sheet_name='统计信息')
    print("  统计信息:")
    print(stats.to_string(index=False))
except Exception as e:
    print(f"  读取统计信息时出错: {e}")

# 4. 查看监考员安排表中有未安排场次的教师
print("\n4. 有未安排场次的教师:")
teacher_result = pd.read_excel(result_file, sheet_name='监考员安排')
unassigned_teachers = teacher_result[teacher_result['未安排场次'] > 0]

if not unassigned_teachers.empty:
    print("  未安排场次的教师详情:")
    for index, row in unassigned_teachers.iterrows():
        print(f"    {row['监考老师']}: 限制{row['场次限制']}场, 实际{row['实际安排次数']}场, 未安排{row['未安排场次']}场")
        
        # 查看该教师的约束条件
        constraints = []
        if pd.notna(row['必监考科目']) and str(row['必监考科目']).strip() != '':
            constraints.append(f"必监考科目: {row['必监考科目']}")
        if pd.notna(row['不监考科目']) and str(row['不监考科目']).strip() != '':
            constraints.append(f"不监考科目: {row['不监考科目']}")
        if pd.notna(row['必监考考场']) and str(row['必监考考场']).strip() != '':
            constraints.append(f"必监考考场: {row['必监考考场']}")
        if pd.notna(row['不监考考场']) and str(row['不监考考场']).strip() != '':
            constraints.append(f"不监考考场: {row['不监考考场']}")
        
        if constraints:
            print(f"      约束条件: {', '.join(constraints)}")
        else:
            print(f"      约束条件: 无特殊约束")
else:
    print("  没有教师有未安排场次")

# 5. 分析缺1的根本原因
print("\n5. 缺1原因分析:")

# 计算总需求和总供给
total_demand = 0
for index, row in room_result.iterrows():
    for subject in ['语文', '数学', '英语', '理化', '政史']:
        cell_value = str(row[subject])
        if cell_value != 'nan' and cell_value.strip() != '':
            # 每个非空单元格需要1个教师
            total_demand += 1

total_supply = sum(teacher_result['场次限制'])
actual_assigned = sum(teacher_result['实际安排次数'])

print(f"  总需求场次: {total_demand}")
print(f"  总供给场次: {total_supply}")
print(f"  实际分配场次: {actual_assigned}")
print(f"  未分配场次: {total_supply - actual_assigned}")

if total_supply < total_demand:
    print("  → 根本原因: 教师总场次供给不足")
elif len(missing_cases) > 0:
    print("  → 根本原因: 虽然总供给充足，但存在约束冲突导致无法完全分配")
else:
    print("  → 状态: 分配完成，无缺1情况") 