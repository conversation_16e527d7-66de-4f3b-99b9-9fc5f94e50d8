{% extends "admin/base.html" %}

{% block content %}
<!-- 自定义样式 -->
<style>
    :root {
        --primary-color: #2c3e50;      /* 深蓝色，主要文字和标题 */
        --secondary-color: #34495e;    /* 次要文字颜色 */
        --accent-color: #3498db;       /* 强调色，用于按钮和链接 */
        --success-color: #27ae60;      /* 成功状态 */
        --warning-color: #f39c12;      /* 警告状态 */
        --danger-color: #e74c3c;       /* 危险/错误状态 */
        --info-color: #2980b9;         /* 信息状态 */
        --light-bg: #f8f9fa;           /* 浅色背景 */
        --border-color: #dee2e6;       /* 边框颜色 */
    }

    /* 页面整体样式 */
    .container-fluid {
        color: var(--primary-color);
    }

    /* 标题样式 */
    h2 {
        color: var(--primary-color);
        font-weight: 600;
    }

    /* 卡片样式 */
    .card {
        border-color: var(--border-color);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .card-body {
        background-color: white;
    }

    /* 表格样式 */
    .table {
        color: var(--secondary-color);
    }

    .table thead th {
        background-color: var(--light-bg);
        color: var(--primary-color);
        font-weight: 600;
        border-bottom: 2px solid var(--border-color);
    }

    .table tbody tr:hover {
        background-color: rgba(52, 152, 219, 0.05);
    }

    /* 按钮样式 */
    .btn-primary {
        background-color: var(--accent-color);
        border-color: var(--accent-color);
    }

    .btn-primary:hover {
        background-color: #2980b9;
        border-color: #2980b9;
    }

    .btn-secondary {
        background-color: #95a5a6;
        border-color: #95a5a6;
    }

    .btn-info {
        background-color: var(--info-color);
        border-color: var(--info-color);
    }

    .btn-warning {
        background-color: var(--warning-color);
        border-color: var(--warning-color);
    }

    .btn-danger {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    /* 状态标签样式 */
    .badge {
        padding: 0.5em 0.8em;
        font-weight: 500;
    }

    .bg-success {
        background-color: var(--success-color) !important;
    }

    .bg-warning {
        background-color: var(--warning-color) !important;
    }

    .bg-danger {
        background-color: var(--danger-color) !important;
    }

    .bg-info {
        background-color: var(--info-color) !important;
    }

    /* 分页样式 */
    .pagination .page-link {
        color: var(--accent-color);
    }

    .pagination .page-item.active .page-link {
        background-color: var(--accent-color);
        border-color: var(--accent-color);
    }

    /* 表单控件样式 */
    .form-control:focus, .form-select:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    /* 复选框样式 */
    .form-check-input:checked {
        background-color: var(--accent-color);
        border-color: var(--accent-color);
    }
</style>

<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>任务管理</h2>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> 返回首页
        </a>
    </div>

    <!-- 筛选区域 -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="filterForm" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">搜索任务</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ search_query }}" placeholder="输入标题或描述关键词">
                </div>
                <div class="col-md-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" name="username"
                           value="{{ username }}" placeholder="输入用户名">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">任务状态</label>
                    <select class="form-select" id="status" name="status">
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量操作工具栏 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-auto">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll">全选</label>
                    </div>
                </div>
                <div class="col">
                    <button class="btn btn-danger me-2" onclick="batchAction('delete')" id="batchDeleteBtn" disabled>
                <i class="fas fa-trash"></i> 批量删除
            </button>
                    <button class="btn btn-warning" onclick="batchAction('reset')" id="batchResetBtn" disabled>
                        <i class="fas fa-undo"></i> 批量重置
            </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="40px"></th>
                            <th width="80px">ID</th>
                            <th>标题</th>
                            <th>创建者</th>
                            <th>创建时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks.items %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input task-checkbox"
                                       value="{{ task.id }}">
                            </td>
                            <td>{{ task.id }}</td>
                            <td>{{ task.title }}</td>
                            <td>{{ task.user.username }}</td>
                            <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <span class="badge bg-{{ task.status_color }}">
                                    {{ task.status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('admin_task_detail', task_id=task.id) }}"
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-warning"
                                            onclick="resetTask('{{ task.id }}')">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger"
                                            onclick="deleteTask('{{ task.id }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页导航 -->
            {% if tasks.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item {% if not tasks.has_prev %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('admin_tasks', page=tasks.prev_num, search=search_query, username=username, status=status) }}">
                            上一页
                        </a>
                    </li>
                    {% for page_num in tasks.iter_pages(left_edge=2, left_current=2, right_current=2, right_edge=2) %}
                        {% if page_num %}
                            <li class="page-item {% if page_num == tasks.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('admin_tasks', page=page_num, search=search_query, username=username, status=status) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    <li class="page-item {% if not tasks.has_next %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('admin_tasks', page=tasks.next_num, search=search_query, username=username, status=status) }}">
                            下一页
                        </a>
                    </li>
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
// 全选/取消全选
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.getElementsByClassName('task-checkbox');
    for (let checkbox of checkboxes) {
        checkbox.checked = this.checked;
    }
    updateBatchButtons();
});

// 更新批量操作按钮状态
function updateBatchButtons() {
    const checkedBoxes = document.querySelectorAll('.task-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    const batchResetBtn = document.getElementById('batchResetBtn');

    batchDeleteBtn.disabled = checkedBoxes.length === 0;
    batchResetBtn.disabled = checkedBoxes.length === 0;
}

// 监听单个复选框变化
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('task-checkbox')) {
        updateBatchButtons();
    }
});

// 重置筛选条件
function resetFilters() {
    document.getElementById('search').value = '';
    document.getElementById('username').value = '';
    document.getElementById('status').value = 'all';
    document.getElementById('filterForm').submit();
}

// 批量操作
function batchAction(action) {
    const checkboxes = document.querySelectorAll('.task-checkbox:checked');
    const taskIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

    if (taskIds.length === 0) {
        alert('请选择要操作的任务');
        return;
    }

    const confirmMessage = action === 'delete' ? '确定要删除选中的任务吗？' : '确定要重置选中的任务状态吗？';
    if (!confirm(confirmMessage)) {
        return;
    }

    fetch('/admin/tasks/batch-action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
        body: JSON.stringify({
            task_ids: taskIds,
            action: action
        })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
            alert(data.message);
                location.reload();
            } else {
            alert('操作失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        alert('操作失败，请稍后重试');
    });
}

// 重置单个任务
function resetTask(taskId) {
    if (!confirm('确定要重置此任务的状态吗？')) {
        return;
    }

    fetch(`/admin/tasks/${taskId}/reset`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
            alert(data.message);
                location.reload();
            } else {
                alert('重置失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        alert('重置失败，请稍后重试');
    });
}

// 删除单个任务
function deleteTask(taskId) {
    if (!confirm('确定要删除此任务吗？')) {
        return;
    }

    fetch(`/admin/tasks/${taskId}/delete`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
                        location.reload();
                    } else {
            alert('删除失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
        alert('删除失败，请稍后重试');
    });
}
</script>
{% endblock %}