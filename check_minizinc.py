#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MiniZinc检查工具：用于检查MiniZinc和求解器的安装状态
"""

import sys
import os
import subprocess
import logging
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

def check_minizinc_python():
    """检查Python MiniZinc绑定是否已安装"""
    try:
        import minizinc
        logging.info(f"MiniZinc Python绑定已安装: {minizinc.__version__}")
        return True
    except ImportError:
        logging.error("未安装MiniZinc Python绑定。请运行: pip install minizinc")
        return False

def check_minizinc_executable():
    """检查MiniZinc可执行文件是否在PATH中"""
    minizinc_path = shutil.which("minizinc")
    if minizinc_path:
        logging.info(f"找到MiniZinc可执行文件: {minizinc_path}")
        
        # 获取版本信息
        try:
            result = subprocess.run(["minizinc", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logging.info(f"MiniZinc版本: {result.stdout.strip()}")
            else:
                logging.warning(f"无法获取MiniZinc版本: {result.stderr}")
        except Exception as e:
            logging.warning(f"检查MiniZinc版本出错: {str(e)}")
        
        return True
    else:
        logging.error("MiniZinc可执行文件未在PATH中。请从 https://www.minizinc.org/software.html 下载并安装MiniZinc")
        return False

def check_solvers():
    """检查可用的MiniZinc求解器"""
    try:
        import minizinc
        solvers = minizinc.Solver.lookup_all()
        if solvers:
            logging.info("找到以下求解器:")
            for solver in solvers:
                logging.info(f"  - {solver.name}: {solver.version}")
            return True
        else:
            logging.error("未找到任何MiniZinc求解器")
            return False
    except ImportError:
        logging.error("由于缺少MiniZinc Python绑定，无法检查求解器")
        return False
    except Exception as e:
        logging.error(f"检查求解器时出错: {str(e)}")
        return False

def check_gecode():
    """特别检查Gecode求解器"""
    try:
        import minizinc
        solver = minizinc.Solver.lookup("gecode")
        if solver:
            logging.info(f"Gecode求解器可用: {solver.version}")
            return True
        else:
            logging.error("未找到Gecode求解器")
            return False
    except ImportError:
        logging.error("由于缺少MiniZinc Python绑定，无法检查Gecode求解器")
        return False
    except Exception as e:
        logging.error(f"检查Gecode求解器时出错: {str(e)}")
        return False

def suggest_fix():
    """提供修复建议"""
    logging.info("\n修复建议:")
    logging.info("1. 安装MiniZinc (https://www.minizinc.org/software.html)")
    logging.info("2. 确保MiniZinc安装目录已添加到系统PATH")
    logging.info("3. 安装Python绑定: pip install minizinc")
    logging.info("4. 在Windows上，可能需要重启电脑使PATH变更生效")
    logging.info("5. 如果已安装但仍检测不到，尝试指定MiniZinc目录:")
    logging.info("   import os")
    logging.info("   os.environ['PATH'] += ';C:\\\\Program Files\\\\MiniZinc'")

def main():
    """主函数"""
    logging.info("开始检查MiniZinc环境...")
    
    python_ok = check_minizinc_python()
    executable_ok = check_minizinc_executable()
    
    if python_ok:
        solvers_ok = check_solvers()
        gecode_ok = check_gecode()
    else:
        solvers_ok = False
        gecode_ok = False
    
    # 总结
    logging.info("\n检查结果:")
    logging.info(f"1. MiniZinc Python绑定: {'✓' if python_ok else '✗'}")
    logging.info(f"2. MiniZinc可执行文件: {'✓' if executable_ok else '✗'}")
    logging.info(f"3. MiniZinc求解器: {'✓' if solvers_ok else '✗'}")
    logging.info(f"4. Gecode求解器: {'✓' if gecode_ok else '✗'}")
    
    if not (python_ok and executable_ok and solvers_ok and gecode_ok):
        suggest_fix()
        return 1
    else:
        logging.info("\nMiniZinc环境检查通过，可以使用MiniZinc进行监考安排！")
        return 0

if __name__ == "__main__":
    sys.exit(main()) 