{% extends "base.html" %}

{% block title %}{{ title }} - 均程通用监考安排{% endblock %}

{% block extra_css %}
<style>
    .register-container {
        max-width: 500px;
        margin: 0 auto;
    }

    .register-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }

    .register-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        padding: 25px 20px;
        position: relative;
        overflow: hidden;
    }

    .register-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        bottom: -50%;
        left: -50%;
        background: linear-gradient(to bottom right, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
        transform: rotate(30deg);
    }

    .register-header h3 {
        position: relative;
        z-index: 1;
        margin: 0;
        font-weight: 600;
    }

    .register-body {
        padding: 30px;
    }

    .form-floating > label {
        color: var(--text-secondary);
    }

    .form-control:focus {
        border-color: var(--primary-light);
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    }

    .register-btn {
        padding: 12px;
        font-weight: 500;
        letter-spacing: 0.5px;
        transition: all 0.3s;
    }

    .register-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .register-footer {
        padding: 20px 30px;
        background-color: rgba(0, 0, 0, 0.02);
    }

    .register-footer a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s;
    }

    .register-footer a:hover {
        color: var(--primary-light);
        text-decoration: underline;
    }

    .password-toggle {
        cursor: pointer;
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
        z-index: 10;
    }

    .password-strength {
        height: 5px;
        border-radius: 5px;
        margin-top: 5px;
        transition: all 0.3s;
    }

    .password-feedback {
        font-size: 0.8rem;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="register-container my-5">
    <div class="text-center mb-4">
        <i class="fas fa-user-plus fa-3x mb-3" style="color: var(--primary-color);"></i>
        <h2 class="fw-bold">创建账号</h2>
        <p class="text-muted">注册新账号以使用均程通用监考安排</p>
    </div>

    <div class="card register-card shadow">
        <div class="register-header text-white">
            <h3><i class="fas fa-user-plus me-2"></i> 用户注册</h3>
        </div>
        <div class="register-body">
            <form method="POST" action="{{ url_for('register') }}" id="registerForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="form-floating mb-3">
                    <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                    <label for="username"><i class="fas fa-user me-2"></i>用户名</label>
                    <div class="form-text">用户名将用于登录和显示</div>
                </div>

                <div class="form-floating mb-3">
                    <input type="email" class="form-control" id="email" name="email" placeholder="电子邮箱" required>
                    <label for="email"><i class="fas fa-envelope me-2"></i>电子邮箱</label>
                    <div class="form-text">我们不会向任何人分享您的邮箱</div>
                </div>

                <div class="form-floating mb-3 position-relative">
                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                    <label for="password"><i class="fas fa-lock me-2"></i>密码</label>
                    <span class="password-toggle" id="passwordToggle">
                        <i class="fas fa-eye"></i>
                    </span>
                    <div class="password-strength" id="passwordStrength"></div>
                    <div class="password-feedback text-muted" id="passwordFeedback">请输入密码</div>
                </div>

                <div class="form-floating mb-4">
                    <input type="password" class="form-control" id="confirmPassword" placeholder="确认密码" required>
                    <label for="confirmPassword"><i class="fas fa-lock me-2"></i>确认密码</label>
                </div>

                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                        <label class="form-check-label" for="agreeTerms">
                            我同意 <a href="#">服务条款</a> 和 <a href="#">隐私政策</a>
                        </label>
                    </div>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary register-btn">
                        <i class="fas fa-user-plus me-2"></i>创建账号
                    </button>
                </div>
            </form>
        </div>
        <div class="register-footer text-center">
            <p class="mb-0">已有账号？ <a href="{{ url_for('login') }}">立即登录</a></p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password visibility toggle
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordInput = document.getElementById('password');

        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle eye icon
            const eyeIcon = this.querySelector('i');
            eyeIcon.classList.toggle('fa-eye');
            eyeIcon.classList.toggle('fa-eye-slash');
        });

        // Password strength meter
        const passwordStrength = document.getElementById('passwordStrength');
        const passwordFeedback = document.getElementById('passwordFeedback');

        passwordInput.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;
            let feedback = '';

            if (password.length > 0) {
                // Length check
                if (password.length >= 8) {
                    strength += 1;
                } else {
                    feedback = '密码应至少有8个字符';
                }

                // Complexity checks
                if (/[A-Z]/.test(password)) strength += 1;
                if (/[0-9]/.test(password)) strength += 1;
                if (/[^A-Za-z0-9]/.test(password)) strength += 1;

                // Set color and width based on strength
                let color = '';
                let width = '';

                switch(strength) {
                    case 0:
                    case 1:
                        color = '#dc3545'; // red
                        width = '25%';
                        if (!feedback) feedback = '密码弱';
                        break;
                    case 2:
                        color = '#ffc107'; // yellow
                        width = '50%';
                        if (!feedback) feedback = '密码中等';
                        break;
                    case 3:
                        color = '#0d6efd'; // blue
                        width = '75%';
                        if (!feedback) feedback = '密码强';
                        break;
                    case 4:
                        color = '#198754'; // green
                        width = '100%';
                        feedback = '密码非常强';
                        break;
                }

                passwordStrength.style.width = width;
                passwordStrength.style.backgroundColor = color;
                passwordStrength.style.display = 'block';
            } else {
                passwordStrength.style.display = 'none';
                feedback = '请输入密码';
            }

            passwordFeedback.textContent = feedback;
        });

        // Form validation
        const registerForm = document.getElementById('registerForm');
        const confirmPassword = document.getElementById('confirmPassword');

        registerForm.addEventListener('submit', function(event) {
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = passwordInput.value;
            const confirmPwd = confirmPassword.value;

            if (username === '' || email === '' || password === '') {
                event.preventDefault();
                alert('请填写所有必填字段');
                return;
            }

            if (password !== confirmPwd) {
                event.preventDefault();
                alert('两次输入的密码不一致');
                return;
            }

            if (password.length < 8) {
                event.preventDefault();
                alert('密码应至少有8个字符');
                return;
            }
        });
    });
</script>
{% endblock %}
