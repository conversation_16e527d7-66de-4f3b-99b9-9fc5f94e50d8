{% extends "base.html" %}

{% block title %}验证测试 - 监考安排系统{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background-color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        box-shadow: var(--card-shadow);
    }

    .test-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
        margin-bottom: 20px;
    }

    .test-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        padding: 25px 20px;
        position: relative;
        overflow: hidden;
    }

    .test-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        bottom: -50%;
        left: -50%;
        background: linear-gradient(to bottom right, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
        transform: rotate(30deg);
    }

    .test-header h3 {
        position: relative;
        z-index: 1;
        margin: 0;
        font-weight: 600;
        color: white;
    }

    .test-body {
        padding: 30px;
    }

    .file-upload-container {
        border: 2px dashed rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        padding: 30px 20px;
        text-align: center;
        transition: all 0.3s;
        cursor: pointer;
        margin-bottom: 20px;
    }

    .file-upload-container:hover {
        border-color: var(--primary-color);
    }

    .file-upload-icon {
        font-size: 3rem;
        color: var(--primary-light);
        margin-bottom: 15px;
    }

    .file-upload-text {
        margin-bottom: 10px;
    }

    .file-upload-input {
        position: absolute;
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        z-index: -1;
    }

    .file-preview {
        display: none;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .file-preview-icon {
        font-size: 2rem;
        color: var(--primary-color);
        margin-right: 15px;
    }

    .file-preview-name {
        margin-bottom: 5px;
        font-weight: 600;
    }

    .file-preview-size {
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    .file-preview-remove {
        cursor: pointer;
        color: var(--danger-color);
        font-size: 1.2rem;
        transition: all 0.2s;
    }

    .file-preview-remove:hover {
        transform: scale(1.1);
    }

    .file-name {
        font-weight: 600;
        color: var(--primary-color);
        word-break: break-all;
        background: rgba(0,0,0,0.05);
        padding: 8px 12px;
        border-radius: 6px;
        margin: 0;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <h2 class="mt-2 mb-0 fw-bold">Excel文件验证测试</h2>
    <p class="text-muted">上传Excel文件进行验证测试</p>
</div>

<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="test-card">
            <div class="test-header">
                <h3><i class="fas fa-check-circle me-2"></i> 验证测试</h3>
            </div>
            <div class="test-body">
                <div class="mb-4">
                    <label class="form-label fw-medium">上传Excel文件</label>
                    <div class="file-upload-container" id="fileUploadContainer">
                        <div class="file-upload-icon">
                            <i class="fas fa-file-excel"></i>
                        </div>
                        <div id="uploadPrompt">
                            <h5 class="file-upload-text">拖放文件到此处或点击选择</h5>
                            <p class="text-muted small">支持 .xlsx 格式的Excel文件</p>
                            <button type="button" class="btn btn-outline-primary btn-sm">选择文件</button>
                        </div>
                        <div id="fileInfo" style="display: none;">
                            <h5 class="file-upload-text mb-2">已选择文件</h5>
                            <p class="file-name mb-3" id="selectedFileName"></p>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="changeFile">
                                <i class="fas fa-exchange-alt me-1"></i>更换文件
                            </button>
                        </div>
                        <input class="file-upload-input" type="file" id="file" name="file" accept=".xlsx">
                    </div>

                    <div class="file-preview" id="filePreview">
                        <div class="d-flex align-items-center">
                            <div class="file-preview-icon">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="file-preview-name" id="fileName"></h6>
                                <p class="file-preview-size mb-0" id="fileSize"></p>
                            </div>
                            <div class="file-preview-remove" id="fileRemove">
                                <i class="fas fa-times"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 验证进度和结果 -->
                    <div id="validationSection" class="mt-3" style="display: none;">
                        <div class="progress mb-2" style="height: 10px;">
                            <div id="validationProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="validationStatus" class="small text-muted"></div>

                        <!-- 验证结果 -->
                        <div id="validationResult" class="mt-3" style="display: none;">
                            <div id="validationSuccess" class="alert alert-success" style="display: none;">
                                <i class="fas fa-check-circle me-2"></i> 文件验证通过！
                                <div id="warningDetails" class="mt-2" style="display: none;">
                                    <h6 class="mb-2">警告信息：</h6>
                                    <ul class="mb-0" id="warningList"></ul>
                                </div>
                            </div>
                            <div id="validationError" class="alert alert-danger" style="display: none;">
                                <i class="fas fa-exclamation-circle me-2"></i> 文件验证失败！
                                <div id="errorDetails" class="mt-2">
                                    <h6 class="mb-2">错误信息：</h6>
                                    <ol class="mb-0" id="errorList"></ol>
                                </div>
                                <div class="mt-2">
                                    <a id="downloadErrorReport" href="#" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-download me-1"></i> 下载错误报告
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="button" id="validateButton" class="btn btn-primary btn-lg">
                        <i class="fas fa-check-circle me-2"></i> 验证文件
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="test-card">
            <div class="test-header">
                <h3><i class="fas fa-info-circle me-2"></i> 验证说明</h3>
            </div>
            <div class="test-body">
                <h6 class="mb-3">验证内容</h6>
                <ul class="ps-3">
                    <li>文件格式是否正确</li>
                    <li>必要的工作表是否存在</li>
                    <li>必要的列是否存在</li>
                    <li>数据类型是否正确</li>
                    <li>数据一致性是否正确</li>
                    <li>数据逻辑关系是否正确</li>
                </ul>

                <h6 class="mb-3 mt-4">必要的工作表</h6>
                <ul class="ps-3">
                    <li>监考员设置</li>
                    <li>考试科目设置</li>
                    <li>考场设置</li>
                </ul>

                <div class="alert alert-info mt-4 mb-0">
                    <i class="fas fa-lightbulb me-2"></i> 提示：验证失败时，可以下载错误报告查看详细信息。
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('file');
        const fileUploadContainer = document.getElementById('fileUploadContainer');
        const filePreview = document.getElementById('filePreview');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const fileRemove = document.getElementById('fileRemove');
        const validateButton = document.getElementById('validateButton');
        const uploadPrompt = document.getElementById('uploadPrompt');
        const fileInfo = document.getElementById('fileInfo');
        const selectedFileName = document.getElementById('selectedFileName');
        const changeFileBtn = document.getElementById('changeFile');

        // 添加验证ID变量
        let currentValidationId = null;

        // 点击上传区域触发文件选择
        fileUploadContainer.addEventListener('click', function() {
            fileInput.click();
        });

        // 拖拽文件上传
        fileUploadContainer.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            fileUploadContainer.style.borderColor = 'var(--primary-color)';
            fileUploadContainer.style.backgroundColor = 'rgba(var(--primary-rgb), 0.05)';
        }, false);

        fileUploadContainer.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            fileUploadContainer.style.borderColor = 'rgba(0, 0, 0, 0.1)';
            fileUploadContainer.style.backgroundColor = '';
        }, false);

        fileUploadContainer.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length) {
                fileInput.files = files;
                updateFilePreview();
            }

            fileUploadContainer.style.borderColor = 'rgba(0, 0, 0, 0.1)';
            fileUploadContainer.style.backgroundColor = '';
        }

        // 处理文件选择变化
        fileInput.addEventListener('change', updateFilePreview);

        function updateFilePreview() {
            if (fileInput.files && fileInput.files[0]) {
                const file = fileInput.files[0];

                // 检查文件扩展名
                const extension = file.name.split('.').pop().toLowerCase();
                if (extension !== 'xlsx') {
                    alert('请选择.xlsx格式的Excel文件');
                    fileInput.value = '';
                    return;
                }

                // 更新预览
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileUploadContainer.style.display = 'none';
                filePreview.style.display = 'block';
            }
        }

        // 更新文件信息显示
        function updateFileInfo(file) {
            if (file) {
                // 显示文件名
                selectedFileName.textContent = file.name;

                // 切换显示
                uploadPrompt.style.display = 'none';
                fileInfo.style.display = 'block';

                // 重置验证状态
                resetValidation();

                // 自动开始验证
                startValidation();
            } else {
                // 重置显示
                uploadPrompt.style.display = 'block';
                fileInfo.style.display = 'none';
                selectedFileName.textContent = '';
                resetValidation();
            }
        }

        // 清理文件和验证状态
        function resetValidation() {
            document.getElementById('validationSection').style.display = 'none';
            document.getElementById('validationResult').style.display = 'none';
            document.getElementById('validationSuccess').style.display = 'none';
            document.getElementById('validationError').style.display = 'none';
            document.getElementById('validationProgress').style.width = '0%';
            document.getElementById('validationStatus').textContent = '';
            document.getElementById('errorList').innerHTML = '';
            document.getElementById('warningList').innerHTML = '';
            document.getElementById('warningDetails').style.display = 'none';
        }

        // 开始验证过程
        function startValidation() {
            if (!fileInput.files || !fileInput.files[0]) {
                alert('请先选择文件');
                return;
            }

            // 重置验证ID
            currentValidationId = null;

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            // 显示验证进度区域
            document.getElementById('validationSection').style.display = 'block';
            document.getElementById('validationProgress').style.width = '0%';
            document.getElementById('validationStatus').textContent = '正在准备验证...';

            // 发送验证请求
            fetch('/task/validate', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.validation_id) {
                    checkValidationProgress(data.validation_id);
                } else {
                    showError('验证过程出错');
                }
            })
            .catch(error => {
                showError('验证请求失败: ' + error);
            });
        }

        // 监听文件选择变化
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // 检查文件扩展名
                const extension = file.name.split('.').pop().toLowerCase();
                if (extension !== 'xlsx') {
                    alert('请选择.xlsx格式的Excel文件');
                    fileInput.value = '';
                    updateFileInfo(null);
                    return;
                }
                updateFileInfo(file);
            } else {
                updateFileInfo(null);
            }
        });

        // 更换文件按钮点击事件
        changeFileBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            fileInput.click();
        });

        // 验证按钮点击事件
        validateButton.addEventListener('click', function() {
            startValidation();
        });

        // 检查验证进度
        function checkValidationProgress(validationId) {
            if (!validationId) {
                showError('验证ID无效');
                return;
            }

            // 保存当前验证ID
            currentValidationId = validationId;

            const progressBar = document.getElementById('validationProgress');
            const statusText = document.getElementById('validationStatus');

            fetch(`/task/validate/${validationId}/progress`)
                .then(response => response.json())
                .then(data => {
                    progressBar.style.width = data.progress + '%';
                    statusText.textContent = `验证进度: ${data.progress}%`;

                    if (data.completed) {
                        showValidationResult(data.result);
                    } else {
                        setTimeout(() => checkValidationProgress(validationId), 1000);
                    }
                })
                .catch(error => {
                    showError('获取验证进度失败: ' + error);
                });
        }

        // 显示验证结果
        function showValidationResult(result) {
            const validationResult = document.getElementById('validationResult');
            const successAlert = document.getElementById('validationSuccess');
            const errorAlert = document.getElementById('validationError');
            const errorList = document.getElementById('errorList');
            const warningList = document.getElementById('warningList');
            const warningDetails = document.getElementById('warningDetails');
            const downloadErrorReport = document.getElementById('downloadErrorReport');

            validationResult.style.display = 'block';
            errorList.innerHTML = '';
            warningList.innerHTML = '';

            if (result.is_valid) {
                // 验证通过
                successAlert.style.display = 'block';
                errorAlert.style.display = 'none';

                // 显示警告信息（如果有）
                if (result.warnings && result.warnings.length > 0) {
                    warningDetails.style.display = 'block';
                    result.warnings.forEach(warning => {
                        const li = document.createElement('li');
                        li.textContent = warning;
                        warningList.appendChild(li);
                    });
                } else {
                    warningDetails.style.display = 'none';
                }
            } else {
                // 验证失败
                successAlert.style.display = 'none';
                errorAlert.style.display = 'block';

                // 显示错误信息
                if (result.errors && result.errors.length > 0) {
                    const ol = document.createElement('ol');
                    ol.className = 'mb-0';

                    result.errors.forEach(error => {
                        const li = document.createElement('li');
                        li.textContent = error;
                        ol.appendChild(li);
                    });

                    errorList.appendChild(ol);
                }

                // 更新错误报告下载链接
                if (result.error_report) {
                    downloadErrorReport.href = `/task/error-report/${currentValidationId}`;
                    downloadErrorReport.style.display = 'inline-block';
                } else {
                    downloadErrorReport.style.display = 'none';
                }
            }
        }

        // 显示错误消息
        function showError(message) {
            const validationResult = document.getElementById('validationResult');
            const successAlert = document.getElementById('validationSuccess');
            const errorAlert = document.getElementById('validationError');
            const errorList = document.getElementById('errorList');

            validationResult.style.display = 'block';
            successAlert.style.display = 'none';
            errorAlert.style.display = 'block';
            errorList.innerHTML = `<li>${message}</li>`;
        }
    });
</script>
{% endblock %}
