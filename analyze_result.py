#!/usr/bin/env python3
import pandas as pd
import glob
import os

def analyze_results():
    # 获取最新的结果文件
    result_files = glob.glob('安排结果_*.xlsx')
    if not result_files:
        print('未找到结果文件')
        return
    
    latest_file = max(result_files, key=lambda x: os.path.getctime(x))
    print(f'分析文件: {latest_file}\n')
    
    # 读取所有工作表
    excel = pd.read_excel(latest_file, sheet_name=None)
    
    # 分析统计信息
    if '统计信息' in excel:
        stats_df = excel['统计信息']
        print('统计信息:')
        print('-' * 50)
        for _, row in stats_df.iterrows():
            print(f"{row['统计项']}: {row['数值']}")
        print('-' * 50)
    
    # 分析监考员安排
    if '监考员安排' in excel:
        teacher_df = excel['监考员安排']
        total_teachers = len(teacher_df)
        satisfied_teachers = len(teacher_df[teacher_df['实际安排次数'] == teacher_df['场次限制']])
        print('\n监考员安排分析:')
        print('-' * 50)
        print(f'总教师数: {total_teachers}')
        print(f'场次要求满足的教师数: {satisfied_teachers}')
        print(f'满足率: {(satisfied_teachers/total_teachers*100):.2f}%')
        print('-' * 50)
    
    # 分析考场安排
    if '考场安排' in excel:
        room_df = excel['考场安排']
        print('\n考场安排分析:')
        print('-' * 50)
        print(f'总考场数: {len(room_df)}')
        # 统计缺人的情况
        missing_count = 0
        for col in room_df.columns:
            if col != '考场':
                missing_count += room_df[col].str.contains('缺', na=False).sum()
        print(f'缺人的安排数: {missing_count}')
        print('-' * 50)

if __name__ == '__main__':
    analyze_results() 