{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2 mb-0">任务详情</h1>
        <div class="btn-group">
            <a href="{{ url_for('admin_tasks') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
            <button type="button" class="btn btn-primary edit-task" data-bs-toggle="modal" data-bs-target="#editTaskModal">
                <i class="fas fa-edit"></i> 编辑任务
            </button>
            {% if task.status == 'pending' %}
            <a href="{{ url_for('admin_schedule_task', task_id=task.id) }}" class="btn btn-success start-task">
                <i class="fas fa-play"></i> 启动监考安排
            </a>
            {% endif %}
            {% if task.status != 'completed' %}
            <button type="button" class="btn btn-warning reset-task">
                <i class="fas fa-redo"></i> 重置状态
            </button>
            {% endif %}
            <button type="button" class="btn btn-danger delete-task">
                <i class="fas fa-trash"></i> 删除任务
            </button>
        </div>
    </div>

    <div class="row">
        <!-- 基本信息卡片 -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">基本信息</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 120px;">任务ID：</th>
                            <td>{{ task.id }}</td>
                        </tr>
                        <tr>
                            <th>任务标题：</th>
                            <td>{{ task.title }}</td>
                        </tr>
                        <tr>
                            <th>创建用户：</th>
                            <td>{{ task.user.username }}</td>
                        </tr>
                        <tr>
                            <th>创建时间：</th>
                            <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        </tr>
                        <tr>
                            <th>更新时间：</th>
                            <td>{{ task.updated_at.strftime('%Y-%m-%d %H:%M:%S') if task.updated_at else '-' }}</td>
                        </tr>
                        <tr>
                            <th>任务状态：</th>
                            <td>
                                <span class="badge bg-{{ task.status_color }}">
                                    {{ task.status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>处理进度：</th>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar"
                                         style="width: {{ task.progress }}%; min-width: 2em;"
                                         aria-valuenow="{{ task.progress }}"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                        {{ task.progress }}%
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- 文件信息卡片 -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">文件信息</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 120px;">模板文件：</th>
                            <td>
                                {% if task.template_file %}
                                <a href="{{ url_for('download_template', task_id=task.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download"></i> 下载模板
                                </a>
                                {% else %}
                                <span class="text-muted">无模板文件</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>输入文件：</th>
                            <td>
                                {% if task.input_file %}
                                <span class="text-success">
                                    <i class="fas fa-check-circle"></i> 已上传
                                </span>
                                {% else %}
                                <span class="text-danger">
                                    <i class="fas fa-times-circle"></i> 未上传
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>输出文件：</th>
                            <td>
                                {% if task.output_file %}
                                <a href="{{ url_for('download_result', task_id=task.id) }}" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-download"></i> 下载结果
                                </a>
                                {% else %}
                                <span class="text-muted">暂无结果文件</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- 处理进度和日志卡片 -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tasks me-2"></i> 处理进度
                        <span class="badge bg-{{ task.status_color }} ms-2" id="statusBadge">{{ task.status_display }}</span>
                    </h5>
                    <div class="d-flex align-items-center">
                        <span class="me-2" id="progressValue">{{ task.progress }}%</span>
                        <div class="progress" style="width: 200px;">
                            <div class="progress-bar bg-{{ task.status_color }}" role="progressbar"
                                 style="width: {{ task.progress }}%;"
                                 aria-valuenow="{{ task.progress }}"
                                 aria-valuemin="0"
                                 aria-valuemax="100" id="progressBar">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-terminal me-2"></i> 处理日志
                            <span class="badge bg-secondary" id="logCount">0 条</span>
                        </h6>
                        <form action="/task/{{ task.id }}/progress" method="get" target="_blank">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-sync-alt"></i> 查看日志
                            </button>
                        </form>
                    </div>
                    <div id="logContainer" class="bg-light p-3 rounded" style="height: 400px; overflow-y: auto; font-family: monospace;">
                        <div id="logContent" class="mb-0">
                            <p class="text-muted">等待日志输出...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务描述卡片 -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">任务描述</h5>
                </div>
                <div class="card-body">
                    {% if task.description %}
                    <p class="card-text">{{ task.description }}</p>
                    {% else %}
                    <p class="card-text text-muted">暂无描述</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 错误信息卡片 -->
        {% if task.error_message %}
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">错误信息</h5>
                </div>
                <div class="card-body">
                    <pre class="text-danger mb-0">{{ task.error_message }}</pre>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 编辑任务模态框 -->
<div class="modal fade" id="editTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editTaskForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="taskTitle" class="form-label">任务标题</label>
                        <input type="text" class="form-control" id="taskTitle" name="title" value="{{ task.title }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">任务描述</label>
                        <textarea class="form-control" id="taskDescription" name="description" rows="3">{{ task.description }}</textarea>
                    </div>
                    <div class="mb-3">
                        <label for="taskFile" class="form-label">更新模板文件（可选）</label>
                        <input type="file" class="form-control" id="taskFile" name="file" accept=".xlsx">
                        <small class="text-muted">仅支持 .xlsx 格式的文件</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveTaskEdit">保存更改</button>
            </div>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除此任务吗？此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 确认重置模态框 -->
<div class="modal fade" id="resetConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认重置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要重置此任务的状态吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" id="confirmReset">确认重置</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    const resetModal = new bootstrap.Modal(document.getElementById('resetConfirmModal'));
    const editModal = document.getElementById('editTaskModal');

    // 删除任务
    document.querySelector('.delete-task').addEventListener('click', function() {
        deleteModal.show();
    });

    document.getElementById('confirmDelete').addEventListener('click', function() {
        fetch('{{ url_for("admin_tasks_delete", task_id=task.id) }}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => {
            if (response.ok) {
                window.location.href = '{{ url_for("admin_tasks") }}';
            } else {
                throw new Error('删除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
        });
    });

    // 重置任务状态
    const resetButton = document.querySelector('.reset-task');
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            resetModal.show();
        });

        document.getElementById('confirmReset').addEventListener('click', function() {
            fetch('{{ url_for("admin_tasks_reset", task_id=task.id) }}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('重置失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            })
            .finally(() => {
                resetModal.hide();
            });
        });
    }

    // 获取日志按钮点击事件
    $('#fetchLogs').click(function() {
        // 显示加载状态
        $(this).html('<i class="fas fa-spinner fa-spin"></i> 加载中...');
        $(this).prop('disabled', true);

        // 获取日志
        $.ajax({
            url: '/task/{{ task.id }}/progress',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                console.log('获取日志成功:', data);

                // 更新进度条
                $('#progressBar').css('width', data.progress + '%');
                $('#progressBar').attr('aria-valuenow', data.progress);
                $('#progressValue').text(data.progress + '%');

                // 更新状态标签
                $('#statusBadge').removeClass().addClass('badge bg-' + data.status_color + ' ms-2').text(data.status_display);

                // 更新日志内容
                if (data.logs && data.logs.length > 0) {
                    $('#logCount').text(data.logs.length + ' 条');

                    let logHtml = '';
                    data.logs.forEach(log => {
                        // 判断是否是进度行
                        if (log.includes('进度:')) {
                            logHtml += `<p class="log-line" style="background-color: rgba(13, 110, 253, 0.1); padding: 4px 8px; border-radius: 4px; margin: 4px 0;">
                                <span style="color: #0d6efd; font-weight: bold;">${log}</span>
                            </p>`;
                        }
                        // 判断是否是错误行
                        else if (log.includes('错误') || log.includes('Error') || log.includes('error') || log.includes('失败')) {
                            logHtml += `<p class="log-line" style="color: #dc3545;">
                                <i class="fas fa-exclamation-circle"></i> ${log}
                            </p>`;
                        }
                        // 判断是否是成功行
                        else if (log.includes('成功') || log.includes('完成')) {
                            logHtml += `<p class="log-line" style="color: #198754;">
                                <i class="fas fa-check-circle"></i> ${log}
                            </p>`;
                        }
                        // 普通日志行
                        else {
                            logHtml += `<p class="log-line">${log}</p>`;
                        }
                    });

                    $('#logContent').html(logHtml);
                    $('#logContainer').scrollTop($('#logContainer')[0].scrollHeight);

                    // 如果任务正在处理中，自动开始轮询
                    if (data.status === 'processing') {
                        setTimeout(function() {
                            $('#fetchLogs').click();
                        }, 3000); // 每3秒自动获取一次
                    } else if (data.status !== '{{ task.status }}') {
                        // 任务状态发生变化时刷新页面
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    }
                } else {
                    $('#logContent').html('<p class="text-muted">暂无日志输出...</p>');
                    $('#logCount').text('0 条');
                }
            },
            error: function(xhr, status, error) {
                console.error('获取日志失败:', error);
                $('#logContent').html('<p class="text-danger">获取日志失败: ' + error + '</p>');
            },
            complete: function() {
                // 恢复按钮状态
                $('#fetchLogs').html('<i class="fas fa-sync-alt"></i> 获取日志');
                $('#fetchLogs').prop('disabled', false);
            }
        });
    });

    // 启动监考安排
    const startButton = document.querySelector('.start-task');
    if (startButton) {
        startButton.addEventListener('click', function(event) {
            if (!confirm('确定要启动监考安排吗？')) {
                event.preventDefault();
                return false;
            }

            // 禁用按钮
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';

            // 继续导航到目标URL
            return true;
        });
    }

    // 手动刷新日志按钮
    const refreshButton = document.getElementById('refreshLogs');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            // 显示加载状态
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';
            this.disabled = true;

            // 获取日志
            updateTaskProgress();

            // 1秒后恢复按钮状态
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新日志';
                this.disabled = false;
            }, 1000);
        });
    }

    // 页面加载完成后自动获取日志
    console.log('页面加载完成，自动获取日志...');
    $('#fetchLogs').click();

    // 如果任务正在处理中，自动开始轮询进度
    if ('{{ task.status }}' === 'processing') {
        console.log('任务正在处理中，将自动轮询进度...');
    } else {
        console.log('任务不在处理中，只获取一次日志');
    }

    // 保存任务编辑
    document.getElementById('saveTaskEdit').addEventListener('click', function() {
        const form = document.getElementById('editTaskForm');
        const formData = new FormData(form);

        fetch('{{ url_for("admin_tasks_edit", task_id=task.id) }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('保存失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('保存失败，请重试');
        });
    });
});
</script>
{% endblock %}