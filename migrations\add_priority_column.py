import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from config import Config
import sqlalchemy as sa
from sqlalchemy import text

app = Flask(__name__)
app.config.from_object(Config)
db = SQLAlchemy(app)

def upgrade():
    """添加priority列到task表"""
    with app.app_context():
        # 检查列是否已存在
        inspector = sa.inspect(db.engine)
        columns = [col['name'] for col in inspector.get_columns('task')]
        if 'priority' not in columns:
            # 使用原生SQL添加列
            db.session.execute(text('ALTER TABLE task ADD COLUMN priority INTEGER DEFAULT 0'))
            db.session.commit()
            print("成功添加priority列")
        else:
            print("priority列已存在")

def downgrade():
    """删除priority列"""
    with app.app_context():
        # 检查列是否存在
        inspector = sa.inspect(db.engine)
        columns = [col['name'] for col in inspector.get_columns('task')]
        if 'priority' in columns:
            # 使用原生SQL删除列
            db.session.execute(text('ALTER TABLE task DROP COLUMN priority'))
            db.session.commit()
            print("成功删除priority列")
        else:
            print("priority列不存在")

if __name__ == '__main__':
    upgrade() 