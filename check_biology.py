import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 读取监考员设置表
df_teachers = pd.read_excel('监考安排.xlsx', sheet_name='监考员设置')

# 查找设置了必监考科目为生物的教师
print("=== 检查生物科目约束 ===")
teachers_with_biology = df_teachers[
    df_teachers['必监考科目'].notna() & 
    df_teachers['必监考科目'].astype(str).str.contains('生物', na=False)
]

if len(teachers_with_biology) > 0:
    print('设置了必监考科目包含生物的教师:')
    for idx, row in teachers_with_biology.iterrows():
        print(f'  {row["监考老师"]}: 必监考科目={row["必监考科目"]}, 场次限制={row["场次限制"]}')
        
        # 检查是否有必监考考场
        if pd.notna(row['必监考考场']) and str(row['必监考考场']) != '0':
            print(f'    必监考考场: {row["必监考考场"]}')
        
        # 检查是否有不监考考场
        if pd.notna(row['不监考考场']) and str(row['不监考考场']) != '0':
            print(f'    不监考考场: {row["不监考考场"]}')
else:
    print('没有教师设置生物为必监考科目')

# 查看考场设置中是否有生物科目
df_rooms = pd.read_excel('监考安排.xlsx', sheet_name='考场设置')
if '生物' in df_rooms.columns:
    print(f'\n生物科目总需求:')
    biology_total = df_rooms['生物'].sum()
    print(f'  总需求教师数: {biology_total}')
    print(f'  有需求的考场:')
    rooms_with_biology = df_rooms[df_rooms['生物'] > 0]
    for idx, row in rooms_with_biology.iterrows():
        print(f'    {row["考场"]}: {row["生物"]}人')
else:
    print('\n考场设置中没有生物科目')

# 检查考试科目设置
df_subjects = pd.read_excel('监考安排.xlsx', sheet_name='考试科目设置')
biology_subjects = df_subjects[df_subjects['课程名称'].str.contains('生物', na=False)]
if len(biology_subjects) > 0:
    print(f'\n生物相关科目:')
    for idx, row in biology_subjects.iterrows():
        print(f'  {row["课程名称"]}: {row["开始时间"]} - {row["结束时间"]}')
else:
    print('\n考试科目设置中没有生物科目') 