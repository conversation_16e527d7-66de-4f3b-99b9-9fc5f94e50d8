#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
监考排课系统启动脚本
用法: python run_scheduler.py -i 输入文件.xlsx -o 输出文件.xlsx --user-id 用户ID --task-id 任务ID
"""

import sys
import os
import argparse
import logging
import pandas as pd
from exam_core_local import ExamScheduler

class SensitiveInfoFilter(logging.Filter):
    def filter(self, record):
        # 只保留包含"处理中"的日志
        if "处理中" in record.msg:
            # 修改日志格式为：时间戳 + "处理中..."
            record.msg = f"{record.msg.split('处理中')[0]}处理中..."
            return True
        return False

class TaskLogger:
    def __init__(self, log_dir, task_id):
        self.log_dir = log_dir
        self.task_id = task_id
        self.main_log_file = os.path.join(log_dir, 'process.log')
        self.progress_file = os.path.join(log_dir, 'progress.log')
        
        # 设置日志处理器
        self.logger = logging.getLogger(f'task_{task_id}')
        self.logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 添加主日志文件处理器
        main_handler = logging.FileHandler(self.main_log_file, encoding='utf-8')
        main_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        main_handler.addFilter(SensitiveInfoFilter())
        self.logger.addHandler(main_handler)
        
        # 添加进度文件处理器
        self.progress_handler = logging.FileHandler(self.progress_file, encoding='utf-8')
        self.progress_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        self.logger.addHandler(self.progress_handler)
        
    def log_progress(self, progress):
        """记录进度信息"""
        self.logger.info(f"处理进度: {progress}%")
        
    def log_status(self, status):
        """记录状态信息"""
        self.logger.info(f"处理中... {status}")

def setup_task_directory(task_id):
    """
    创建任务目录结构
    返回: (任务目录路径, 日志目录路径)
    """
    # 任务目录
    task_dir = os.path.join('uploads', str(task_id))
    # 日志目录
    log_dir = os.path.join(task_dir, 'logs')
    
    # 创建必要的目录
    for directory in [task_dir, log_dir]:
        os.makedirs(directory, exist_ok=True)
        
    return task_dir, log_dir

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='监考排课系统')
    parser.add_argument('--input', '-i', required=True, help='输入Excel文件路径')
    parser.add_argument('--output', '-o', required=True, help='输出Excel文件名')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细日志')
    parser.add_argument('--time-limit', type=int, default=300, help='求解时间限制(秒)')
    parser.add_argument('--max-duration-diff', type=float, default=1.0,
                      help='同组教师间允许的最大监考时长差异(小时)')
    parser.add_argument('--task-id', required=True, help='任务ID')
    parser.add_argument('--task-name', default='安排结果', help='任务名称')

    return parser.parse_args()

def generate_output_filename(task_id, task_name):
    """生成输出文件名
    格式：任务ID_任务名称_时间戳.xlsx
    例如：22_33_20250415_112923.xlsx
    """
    import datetime
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"{task_id}_{task_name}_{timestamp}.xlsx"

def main():
    """主函数"""
    args = None
    task_logger = None
    
    try:
        # 解析命令行参数
        args = parse_args()
        
        # 设置任务目录
        task_dir, log_dir = setup_task_directory(args.task_id)
        
        # 初始化任务日志器
        task_logger = TaskLogger(log_dir, args.task_id)
        
        # 检查输入文件是否存在
        if not os.path.exists(args.input):
            task_logger.log_status(f"错误: 输入文件 '{args.input}' 不存在!")
            sys.exit(1)

        # 生成输出文件名并设置完整路径
        output_filename = generate_output_filename(args.task_id, args.task_name)
        args.output = os.path.join(task_dir, output_filename)
            
        # 记录任务开始
        task_logger.log_status("任务开始")
        task_logger.log_progress(0)
        
        # 设置任务日志器
        if args.verbose:
            os.environ['SCHEDULER_LOG_LEVEL'] = 'DEBUG'
        
        # 设置求解时间限制
        os.environ['SCHEDULER_TIME_LIMIT'] = str(args.time_limit)

        # 创建调度器实例
        scheduler = ExamScheduler(args.input, max_duration_diff=args.max_duration_diff)
        task_logger.log_progress(20)

        # 预处理数据
        task_logger.log_status("正在预处理教师数据")
        scheduler.preprocess_teachers()
        task_logger.log_progress(40)
        
        task_logger.log_status("正在预处理科目数据")
        scheduler.preprocess_subjects()
        task_logger.log_progress(60)

        # 执行监考安排
        task_logger.log_status("正在计算监考安排")
        best_schedule = scheduler.schedule_exams()
        task_logger.log_progress(80)

        # 导出结果
        if best_schedule:
            task_logger.log_status("正在导出结果")
            with pd.ExcelWriter(args.output) as writer:
                scheduler._export_room_assignments(best_schedule, writer)
                scheduler._export_teacher_assignments(best_schedule, writer)
                if hasattr(scheduler, 'scheduler') and scheduler.scheduler:
                    scheduler.scheduler.export_group_duration_data(writer)
                scheduler._export_statistics(best_schedule, writer)
            
            task_logger.log_status("任务完成")
            task_logger.log_progress(100)
        else:
            task_logger.log_status("未能找到可行的监考安排方案")
            sys.exit(1)

    except KeyboardInterrupt:
        if task_logger:
            task_logger.log_status("程序被用户中断")
        sys.exit(0)
    except Exception as e:
        if task_logger:
            task_logger.log_status(f"运行错误: {str(e)}")
        import traceback
        if task_logger:
            task_logger.log_status(traceback.format_exc())
        sys.exit(1)

if __name__ == '__main__':
    main()