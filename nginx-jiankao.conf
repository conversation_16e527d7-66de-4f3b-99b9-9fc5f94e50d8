server {
    listen 80;
    server_name _;  # 替换为您的域名或IP地址

    access_log /var/log/nginx/jiankao_access.log;
    error_log /var/log/nginx/jiankao_error.log;

    # 设置客户端上传文件大小限制
    client_max_body_size 10M;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /opt/jiankao/static;
        expires 30d;
    }

    location /uploads {
        # 禁止直接访问上传目录
        deny all;
        return 404;
    }
}
