{% extends "base.html" %}

{% block title %}{{ title }} - 均程通用监考安排{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        max-width: 450px;
        margin: 0 auto;
    }

    .login-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }

    .login-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        padding: 25px 20px;
        position: relative;
        overflow: hidden;
    }

    .login-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        bottom: -50%;
        left: -50%;
        background: linear-gradient(to bottom right, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
        transform: rotate(30deg);
    }

    .login-header h3 {
        position: relative;
        z-index: 1;
        margin: 0;
        font-weight: 600;
    }

    .login-body {
        padding: 30px;
    }

    .form-floating > label {
        color: var(--text-secondary);
    }

    .form-control:focus {
        border-color: var(--primary-light);
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    }

    .login-btn {
        padding: 12px;
        font-weight: 500;
        letter-spacing: 0.5px;
        transition: all 0.3s;
    }

    .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .login-footer {
        padding: 20px 30px;
        background-color: rgba(0, 0, 0, 0.02);
    }

    .login-footer a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s;
    }

    .login-footer a:hover {
        color: var(--primary-light);
        text-decoration: underline;
    }

    .password-toggle {
        cursor: pointer;
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
        z-index: 10;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container my-5">
    <div class="text-center mb-4">
        <i class="fas fa-calendar-check fa-3x mb-3" style="color: var(--primary-color);"></i>
        <h2 class="fw-bold">均程通用监考安排</h2>
        <p class="text-muted">请登录您的账号以继续</p>
    </div>

    <div class="card login-card shadow">
        <div class="login-header text-white">
            <h3><i class="fas fa-sign-in-alt me-2"></i> 用户登录</h3>
        </div>
        <div class="login-body">
            <form method="POST" action="{{ url_for('login') }}" id="loginForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="form-floating mb-3">
                    <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                    <label for="username"><i class="fas fa-user me-2"></i>用户名</label>
                </div>
                <div class="form-floating mb-3 position-relative">
                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                    <label for="password"><i class="fas fa-lock me-2"></i>密码</label>
                    <span class="password-toggle" id="passwordToggle">
                        <i class="fas fa-eye"></i>
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">记住我</label>
                    </div>
                    <a href="#" class="small">忘记密码？</a>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary login-btn">
                        <i class="fas fa-sign-in-alt me-2"></i>登录系统
                    </button>
                </div>
            </form>
        </div>
        <div class="login-footer text-center">
            <p class="mb-0">还没有账号？ <a href="{{ url_for('register') }}">立即注册</a></p>
        </div>
    </div>

    <div class="text-center mt-4">
        <p class="small text-muted">登录即表示您同意我们的 <a href="#">服务条款</a> 和 <a href="#">隐私政策</a></p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password visibility toggle
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordInput = document.getElementById('password');

        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle eye icon
            const eyeIcon = this.querySelector('i');
            eyeIcon.classList.toggle('fa-eye');
            eyeIcon.classList.toggle('fa-eye-slash');
        });

        // Form validation enhancement
        const loginForm = document.getElementById('loginForm');
        loginForm.addEventListener('submit', function(event) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (username === '' || password === '') {
                event.preventDefault();
                alert('用户名和密码不能为空');
            }
        });
    });
</script>
{% endblock %}
