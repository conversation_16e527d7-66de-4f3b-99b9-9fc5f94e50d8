#!/usr/bin/env python3
"""
智能初始化策略实现

这个模块提供了改进的初始化算法，可以直接替换main.py中的_initialize_schedule方法
目标：通过更智能的初始解生成，显著提高算法的起始适应度分数
"""

from collections import defaultdict, Counter
import random
from typing import List, Dict, Set, Tuple
import pandas as pd

class SmartScheduleInitializer:
    """智能调度初始化器"""
    
    def __init__(self, scheduler):
        self.scheduler = scheduler
        self.teachers = scheduler.teachers
        self.subjects = scheduler.subjects
        self.rooms = scheduler.rooms
        self.subject_map = scheduler.subject_map
        
    def generate_smart_initial_schedule(self):
        """
        智能初始化主函数
        
        策略优先级：
        1. 必监考科目教师优先分配
        2. 稀缺资源科目优先处理
        3. 场次限制严格满足
        4. 考场约束优先考虑
        """
        from main import Schedule
        
        schedule = Schedule()
        
        # 重置教师状态
        for teacher in self.teachers:
            teacher.current_sessions = 0
            teacher.assigned_times = []
        
        # 第一阶段：处理必监考科目的强制分配
        self._phase1_must_subjects_assignment(schedule)
        
        # 第二阶段：处理稀缺资源科目
        self._phase2_scarce_subjects_assignment(schedule)
        
        # 第三阶段：填补剩余需求
        self._phase3_remaining_assignment(schedule)
        
        # 第四阶段：场次平衡优化
        self._phase4_session_balance_optimization(schedule)
        
        return schedule
    
    def _phase1_must_subjects_assignment(self, schedule):
        """
        阶段1：必监考科目教师优先分配
        
        确保有必监考科目要求的教师优先被分配到对应科目
        这是硬约束，必须优先满足
        """
        print("  智能初始化 - 阶段1: 处理必监考科目")
        
        # 收集所有必监考要求
        must_assignments = []
        for teacher in self.teachers:
            if teacher.must_subjects:
                for subject in teacher.must_subjects:
                    must_assignments.append((teacher, subject))
        
        # 按优先级排序：考场约束更严格的教师优先
        must_assignments.sort(key=lambda x: (
            len(x[0].must_rooms),      # 必监考考场数量（越少越优先）
            len(x[0].forbidden_rooms), # 禁止考场数量（越多越优先）
            -x[0].max_sessions         # 场次限制（越少越优先）
        ))
        
        assigned_teachers = set()
        for teacher, subject_name in must_assignments:
            if teacher.name in assigned_teachers:
                continue
                
            # 寻找合适的考场
            suitable_rooms = self._find_suitable_rooms(teacher, subject_name, schedule)
            if suitable_rooms:
                # 选择最佳考场
                best_room = self._select_best_room(teacher, subject_name, suitable_rooms)
                
                # 执行分配
                schedule.add_assignment(subject_name, best_room.name, teacher.name)
                teacher.current_sessions += 1
                assigned_teachers.add(teacher.name)
                
                subject_obj = self.subject_map[subject_name]
                teacher.assigned_times.append((subject_obj.start_time, subject_obj.end_time))
                
                print(f"    必监考分配: {teacher.name} -> {subject_name}({best_room.name})")
    
    def _phase2_scarce_subjects_assignment(self, schedule):
        """
        阶段2：稀缺资源科目优先处理
        
        优先处理教师资源相对稀缺的科目，避免后期无法满足需求
        """
        print("  智能初始化 - 阶段2: 处理稀缺资源科目")
        
        # 计算每个科目的资源稀缺度
        subject_scarcity = self._calculate_subject_scarcity(schedule)
        
        # 按稀缺度排序处理科目
        for subject_name, scarcity_score in sorted(subject_scarcity.items(), 
                                                  key=lambda x: x[1], reverse=True):
            self._assign_subject_with_priority(schedule, subject_name, "稀缺资源")
    
    def _phase3_remaining_assignment(self, schedule):
        """
        阶段3：填补剩余需求
        
        使用改进的贪心策略处理剩余的分配需求
        """
        print("  智能初始化 - 阶段3: 填补剩余需求")
        
        # 按科目时间顺序处理剩余需求
        for subject in self.subjects:
            remaining_needs = self._get_remaining_subject_needs(schedule, subject.name)
            if remaining_needs:
                self._assign_subject_with_priority(schedule, subject.name, "剩余需求")
    
    def _phase4_session_balance_optimization(self, schedule):
        """
        阶段4：场次平衡优化
        
        尝试通过局部调整实现更好的场次平衡
        """
        print("  智能初始化 - 阶段4: 场次平衡优化")
        
        # 识别场次不足的教师
        underassigned_teachers = [
            teacher for teacher in self.teachers
            if teacher.current_sessions < teacher.max_sessions
        ]
        
        # 尝试为场次不足的教师增加分配
        for teacher in underassigned_teachers:
            self._try_increase_teacher_sessions(schedule, teacher)
    
    def _find_suitable_rooms(self, teacher, subject_name, schedule):
        """寻找教师可以监考指定科目的合适考场"""
        suitable_rooms = []
        
        for room in self.rooms:
            # 检查考场是否需要该科目的监考
            if subject_name not in room.subject_requirements:
                continue
            
            # 检查当前分配是否已满
            current_assignments = schedule.assignments.get((subject_name, room.name), [])
            required_count = room.subject_requirements[subject_name]
            if len(current_assignments) >= required_count:
                continue
            
            # 检查教师约束
            if not self._can_teacher_work_in_room(teacher, subject_name, room):
                continue
            
            # 检查时间冲突
            if self._has_time_conflict_smart(schedule, teacher, subject_name):
                continue
            
            suitable_rooms.append(room)
        
        return suitable_rooms
    
    def _can_teacher_work_in_room(self, teacher, subject_name, room):
        """检查教师是否可以在指定考场监考指定科目"""
        # 检查禁止监考科目
        if subject_name in teacher.forbidden_subjects:
            return False
        
        # 检查禁止监考考场
        if room.name in teacher.forbidden_rooms:
            return False
        
        # 检查必监考考场约束
        if teacher.must_rooms and room.name not in teacher.must_rooms:
            return False
        
        # 检查场次限制
        if teacher.current_sessions >= teacher.max_sessions:
            return False
        
        return True
    
    def _select_best_room(self, teacher, subject_name, suitable_rooms):
        """从合适的考场中选择最佳考场"""
        # 评分标准
        def room_score(room):
            score = 0
            
            # 必监考考场优先
            if room.name in teacher.must_rooms:
                score += 100
            
            # 任教科目匹配优先
            if subject_name == teacher.teaching_subject:
                score += 50
            
            # 考场需求紧迫度（剩余需求少的优先）
            required = room.subject_requirements.get(subject_name, 0)
            if required > 0:
                score += 20 / required  # 需求越少越优先
            
            return score
        
        # 选择评分最高的考场
        return max(suitable_rooms, key=room_score)
    
    def _calculate_subject_scarcity(self, schedule):
        """计算各科目的资源稀缺度"""
        scarcity_scores = {}
        
        for subject in self.subjects:
            # 计算总需求
            total_required = sum(
                room.subject_requirements.get(subject.name, 0)
                for room in self.rooms
            )
            
            # 计算已分配数量
            total_assigned = sum(
                len(teachers) for (subj, room), teachers in schedule.assignments.items()
                if subj == subject.name
            )
            
            # 计算可用教师数
            available_teachers = len([
                teacher for teacher in self.teachers
                if (subject.name not in teacher.forbidden_subjects and
                    teacher.current_sessions < teacher.max_sessions and
                    not self._has_time_conflict_smart(schedule, teacher, subject.name))
            ])
            
            # 稀缺度 = (剩余需求) / (可用教师数 + 1)
            remaining_need = max(0, total_required - total_assigned)
            scarcity_score = remaining_need / (available_teachers + 1)
            
            scarcity_scores[subject.name] = scarcity_score
        
        return scarcity_scores
    
    def _assign_subject_with_priority(self, schedule, subject_name, phase_name):
        """使用优先级策略为科目分配教师"""
        # 获取该科目的剩余需求
        remaining_needs = self._get_remaining_subject_needs(schedule, subject_name)
        
        for room_name, (room, needed_count) in remaining_needs.items():
            for _ in range(needed_count):
                # 寻找最佳教师
                best_teacher = self._find_best_teacher_for_assignment(
                    schedule, subject_name, room
                )
                
                if best_teacher:
                    # 执行分配
                    schedule.add_assignment(subject_name, room.name, best_teacher.name)
                    best_teacher.current_sessions += 1
                    
                    subject_obj = self.subject_map[subject_name]
                    best_teacher.assigned_times.append((subject_obj.start_time, subject_obj.end_time))
                    
                    print(f"    {phase_name}分配: {best_teacher.name} -> {subject_name}({room.name})")
                else:
                    print(f"    警告：无法为 {subject_name}({room.name}) 找到合适的教师")
    
    def _get_remaining_subject_needs(self, schedule, subject_name):
        """获取指定科目的剩余需求"""
        remaining_needs = {}
        
        for room in self.rooms:
            if subject_name in room.subject_requirements:
                required_count = room.subject_requirements[subject_name]
                assigned_count = len(schedule.assignments.get((subject_name, room.name), []))
                needed_count = required_count - assigned_count
                
                if needed_count > 0:
                    remaining_needs[room.name] = (room, needed_count)  # 存储(room对象, 需求数量)
        
        return remaining_needs
    
    def _find_best_teacher_for_assignment(self, schedule, subject_name, room):
        """为特定分配寻找最佳教师"""
        # 获取可用教师
        available_teachers = [
            teacher for teacher in self.teachers
            if self._can_teacher_work_in_room(teacher, subject_name, room) and
               not self._has_time_conflict_smart(schedule, teacher, subject_name)
        ]
        
        if not available_teachers:
            return None
        
        # 教师评分函数
        def teacher_score(teacher):
            score = 0
            
            # 必监考科目匹配（最高优先级）
            if subject_name in teacher.must_subjects:
                score += 1000
            
            # 必监考考场匹配
            if room.name in teacher.must_rooms:
                score += 500
            
            # 任教科目匹配
            if subject_name == teacher.teaching_subject:
                score += 100
            
            # 场次平衡（优先分配给场次较少的教师）
            remaining_sessions = teacher.max_sessions - teacher.current_sessions
            score += remaining_sessions * 10
            
            # 工作量平衡
            if teacher.current_sessions < teacher.max_sessions - 1:
                score += 20
            
            return score
        
        # 选择评分最高的教师
        return max(available_teachers, key=teacher_score)
    
    def _try_increase_teacher_sessions(self, schedule, teacher):
        """尝试为教师增加监考场次"""
        # 寻找还有空余位置的分配机会
        for subject in self.subjects:
            if teacher.current_sessions >= teacher.max_sessions:
                break
                
            for room in self.rooms:
                if subject.name in room.subject_requirements:
                    current_assignments = schedule.assignments.get((subject.name, room.name), [])
                    required_count = room.subject_requirements[subject.name]
                    
                    if (len(current_assignments) < required_count and
                        self._can_teacher_work_in_room(teacher, subject.name, room) and
                        not self._has_time_conflict_smart(schedule, teacher, subject.name)):
                        
                        # 执行分配
                        schedule.add_assignment(subject.name, room.name, teacher.name)
                        teacher.current_sessions += 1
                        
                        subject_obj = self.subject_map[subject.name]
                        teacher.assigned_times.append((subject_obj.start_time, subject_obj.end_time))
                        
                        print(f"    场次平衡: {teacher.name} -> {subject.name}({room.name})")
                        break
    
    def _has_time_conflict_smart(self, schedule, teacher, subject_name):
        """智能时间冲突检查"""
        subject_obj = self.subject_map[subject_name]
        new_time = (subject_obj.start_time, subject_obj.end_time)
        
        return any(
            self._is_time_overlap(new_time, existing_time)
            for existing_time in teacher.assigned_times
        )
    
    @staticmethod
    def _is_time_overlap(slot1, slot2):
        """检查两个时间段是否重叠"""
        start1, end1 = slot1
        start2, end2 = slot2
        return max(start1, start2) < min(end1, end2)


# 集成函数：替换现有的初始化方法
def integrate_smart_initialization(scheduler):
    """
    集成智能初始化到现有调度器
    
    使用方法：
    在main.py的ExamScheduler类中，将_initialize_schedule方法替换为：
    
    def _initialize_schedule(self) -> Schedule:
        from improved_initialization import integrate_smart_initialization
        return integrate_smart_initialization(self)
    """
    print("使用智能初始化策略生成初始解...")
    
    initializer = SmartScheduleInitializer(scheduler)
    smart_schedule = initializer.generate_smart_initial_schedule()
    
    # 计算并报告初始适应度
    fitness = smart_schedule.calculate_fitness(scheduler)
    print(f"智能初始化完成，适应度分数: {fitness:.2f}")
    
    return smart_schedule


if __name__ == "__main__":
    print("智能初始化策略模块")
    print("使用说明：")
    print("1. 将此文件放在与main.py同目录下")
    print("2. 在main.py中导入并替换_initialize_schedule方法")
    print("3. 预期效果：显著提高初始解的适应度分数") 