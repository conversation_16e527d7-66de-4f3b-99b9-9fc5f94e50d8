# 监考安排系统部署指南 (Debian环境)

本文档提供了在Debian环境中部署监考安排系统的详细步骤和说明。

## 系统要求

- Debian 10+ 或 Ubuntu 20.04+
- 至少1GB内存
- 至少10GB磁盘空间
- Python 3.8 或更高版本
- 公网IP地址: **************
- 域名: jiankao.her5.com（默认已配置）

## 部署前准备

1. 确保系统已更新
   ```bash
   sudo apt update
   sudo apt upgrade -y
   ```

2. 确保您有root权限或sudo权限

3. 如果需要配置SSL证书，请确保您的域名已正确解析到服务器IP

## 部署步骤

### 一键部署

1. 下载部署脚本
   ```bash
   wget https://raw.githubusercontent.com/yourusername/jiankao/main/deploy_debian.sh
   chmod +x deploy_debian.sh
   ```

2. 运行部署脚本
   ```bash
   sudo ./deploy_debian.sh
   ```

3. 按照提示确认域名
   - 默认域名为 jiankao.her5.com，直接按回车使用默认域名
   - 如果您有其他域名，请输入您的域名
   - 如果不想使用域名，请输入 'no'，系统将使用IP地址访问

4. 等待部署完成
   - 脚本会自动安装所有必要的依赖
   - 创建应用目录和配置文件
   - 设置虚拟环境和安装Python依赖
   - 配置Nginx和Supervisor
   - 如果提供了域名，会自动申请SSL证书

### 部署过程详解

部署脚本会按照以下顺序执行操作：

1. 检查root权限
2. 更新软件包列表
3. 安装基本工具（curl, wget, gnupg2等）
4. 按顺序安装Python依赖（python3, python3-venv, python3-pip）
5. 创建应用目录（默认为/opt/jiankao）
6. 创建目录结构（uploads, logs, template-guide等）
7. 创建配置文件（config.py）
8. 创建虚拟环境并安装Python依赖
9. 初始化数据库
10. 安装和配置Supervisor
11. 安装和配置Nginx
12. 如果提供了域名，安装Certbot并配置SSL
13. 创建管理脚本
14. 启动服务

## 配置说明

### 临时文件目录

系统使用 `/tmp/jiankao` 作为临时文件目录，此目录会在部署过程中自动创建。

### 数据库

系统使用SQLite数据库，数据库文件位于 `/opt/jiankao/app.db`。

### 日志文件

- 应用日志：`/opt/jiankao/logs/app.log`
- Gunicorn日志：`/opt/jiankao/logs/gunicorn.log`
- Supervisor日志：`/opt/jiankao/logs/supervisor_*.log`
- Nginx日志：`/var/log/nginx/jiankao_*.log`

### SSL证书

如果您在部署过程中提供了域名，系统会自动申请Let's Encrypt SSL证书。证书会自动续期，无需手动操作。

## 管理系统

部署完成后，您可以使用以下脚本管理系统：

- 启动服务：`/opt/jiankao/start.sh`
- 停止服务：`/opt/jiankao/stop.sh`
- 重启服务：`/opt/jiankao/restart.sh`
- 查看服务状态：`/opt/jiankao/status.sh`

## 访问系统

部署完成后，您可以通过以下方式访问系统：

- 如果配置了域名和SSL：`https://您的域名`
- 如果没有配置域名：`http://服务器IP`

默认管理员账号：
- 用户名：admin
- 密码：admin123

**重要提示：** 首次登录后请立即修改管理员密码！

## 更新SSL证书

如果您需要手动更新SSL证书，可以使用以下命令：

```bash
sudo certbot --nginx -d 您的域名 --non-interactive --agree-tos --redirect
```

## 故障排除

1. **服务无法启动**

   检查Supervisor日志：
   ```bash
   sudo supervisorctl status jiankao
   cat /opt/jiankao/logs/supervisor_err.log
   ```

2. **无法访问网站**

   检查Nginx配置和状态：
   ```bash
   sudo nginx -t
   sudo systemctl status nginx
   ```

3. **SSL证书问题**

   检查Certbot日志：
   ```bash
   sudo certbot certificates
   ```

4. **数据库错误**

   检查应用日志：
   ```bash
   cat /opt/jiankao/logs/app.log
   ```

## 备份与恢复

### 备份

建议定期备份以下文件：

```bash
# 备份数据库
cp /opt/jiankao/app.db /backup/app.db.$(date +%Y%m%d)

# 备份上传文件
tar -czf /backup/uploads.$(date +%Y%m%d).tar.gz /opt/jiankao/uploads

# 备份配置文件
cp /opt/jiankao/config.py /backup/config.py.$(date +%Y%m%d)
```

### 恢复

```bash
# 恢复数据库
cp /backup/app.db.20240420 /opt/jiankao/app.db

# 恢复上传文件
tar -xzf /backup/uploads.20240420.tar.gz -C /

# 恢复配置文件
cp /backup/config.py.20240420 /opt/jiankao/config.py
```

## 安全建议

1. 立即修改默认管理员密码
2. 定期更新系统和依赖包
3. 启用防火墙，只开放必要端口（80, 443）
4. 定期备份数据
5. 使用强密码并定期更换
6. 监控系统日志，及时发现异常
