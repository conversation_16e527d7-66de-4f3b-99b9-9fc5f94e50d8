{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <h1 class="h2 mb-4">添加用户</h1>

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">角色</label>
                                <select class="form-select" id="role" name="role" onchange="updateTaskLimit()">
                                    <option value="user">普通用户</option>
                                    <option value="vip">VIP用户</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="task_limit" class="form-label">任务限制</label>
                                <input type="number" class="form-control" id="task_limit" name="task_limit" value="1" min="1" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">账号启用</label>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">创建用户</button>
                                <a href="{{ url_for('admin_users') }}" class="btn btn-secondary">返回</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function updateTaskLimit() {
    const role = document.getElementById('role').value;
    const taskLimitInput = document.getElementById('task_limit');

    switch(role) {
        case 'vip':
            taskLimitInput.value = '6';
            break;
        case 'user':
            taskLimitInput.value = '1';
            break;
        case 'admin':
            taskLimitInput.value = '999';
            break;
    }
}

// 页面加载时执行一次
document.addEventListener('DOMContentLoaded', updateTaskLimit);
</script>
{% endblock %}