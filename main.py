# 导入所需的Python库
import pandas as pd  # 用于数据处理和Excel文件操作
import numpy as np   # 用于数值计算
from datetime import datetime  # 用于处理日期和时间
from typing import List, Dict, Set, Tuple  # 用于类型注解
from dataclasses import dataclass, field  # 用于创建数据类
import logging  # 用于日志记录
from collections import defaultdict, Counter  # 用于处理集合和计数
import random  # 用于随机数生成
from pathlib import Path  # 用于处理文件路径
from concurrent.futures import ThreadPoolExecutor, as_completed  # 用于并行处理
import os  # 用于操作系统相关功能

# 配置日志记录系统
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别为INFO
    format='%(asctime)s - %(levelname)s - %(message)s',  # 设置日志格式：时间-级别-消息
    handlers=[
        logging.FileHandler('invigilator_scheduler.log'),  # 将日志写入文件
        logging.StreamHandler()  # 同时在控制台显示日志
    ]
)

@dataclass
class Teacher:
    """教师类：存储和管理教师的监考相关信息及限制条件"""
    id: int                     # 教师的唯一标识ID
    name: str                   # 教师姓名
    max_sessions: int           # 教师可以监考的最大场次数
    must_subjects: Set[str] = field(default_factory=set)    # 教师必须监考的科目集合
    forbidden_subjects: Set[str] = field(default_factory=set)  # 教师不能监考的科目集合
    must_rooms: Set[str] = field(default_factory=set)       # 教师必须监考的考场集合
    forbidden_rooms: Set[str] = field(default_factory=set)   # 教师不能监考的考场集合
    teaching_subject: str = ""   # 教师所教授的科目
    special_settings_count: int = 0  # 教师特殊设置的数量（用于优先级计算）
    available_subjects: Set[str] = field(default_factory=set)  # 教师可以监考的科目集合
    min_sessions: int = 0        # 教师必须监考的最小场次数
    current_sessions: int = 0    # 教师当前已安排的监考场次数
    workload_weight: float = 1.0  # 教师工作量权重
    assigned_times: List[Tuple[datetime, datetime]] = field(default_factory=list)  # 教师已被安排的时间段列表

    @classmethod
    def from_excel_row(cls, row):
        """
        从Excel表格的一行数据创建教师对象
        
        参数:
            row: Excel表格中的一行数据
        返回:
            Teacher: 创建的教师对象
        """
        # 处理必须监考和禁止监考的科目与考场
        # 将字符串转换为集合，并去除空白字符
        must_subjects = set(str(row['必监考科目']).split(','))
        forbidden_subjects = set(str(row['不监考科目']).split(','))
        must_rooms = set(str(row['必监考考场']).split(','))
        forbidden_rooms = set(str(row['不监考考场']).split(','))
        
        # 清理数据，移除空字符串和空白字符
        must_subjects = {s.replace(' ', '') for s in must_subjects if s.replace(' ', '')}
        forbidden_subjects = {s.replace(' ', '') for s in forbidden_subjects if s.replace(' ', '')}
        must_rooms = {r.replace(' ', '') for r in must_rooms if r.replace(' ', '')}
        forbidden_rooms = {r.replace(' ', '') for r in forbidden_rooms if r.replace(' ', '')}
        
        # 获取教师任教科目，如果为空则设为空字符串
        teaching_subject = str(row['任教科目']).strip() if pd.notna(row['任教科目']) else ""
        
        # 获取场次限制
        max_sessions = int(row['场次限制'])
        
        # 创建并返回教师对象
        return cls(
            id=int(row['序号']),
            name=str(row['监考老师']),
            must_subjects=must_subjects,
            forbidden_subjects=forbidden_subjects,
            must_rooms=must_rooms,
            forbidden_rooms=forbidden_rooms,
            teaching_subject=teaching_subject,
            max_sessions=max_sessions,
            min_sessions=max_sessions - 1  # 最小场次为最大场次减1
        )

    def calculate_weight(self) -> float:
        """
        计算教师的综合权重分数
        用于确定教师的优先级和分配顺序
        
        返回:
            float: 计算得到的权重分数
        """
        weight = 0.0
        # 计算特殊要求的权重
        weight += len(self.must_subjects) * 2.0    # 必监考科目权重
        weight += len(self.forbidden_subjects) * 1.5  # 禁止监考科目权重
        weight += len(self.must_rooms) * 2.0       # 必监考考场权重
        weight += len(self.forbidden_rooms) * 1.5   # 禁止监考考场权重
        
        # 计算剩余可安排场次的权重
        remaining_sessions = self.max_sessions - self.current_sessions
        weight += remaining_sessions * 1.0
        
        return weight

@dataclass
class Subject:
    """科目类：存储考试科目的相关信息"""
    code: str                   # 科目代码
    name: str                   # 科目名称
    start_time: datetime        # 考试开始时间
    end_time: datetime          # 考试结束时间
    total_required_teachers: int = 0  # 该科目需要的总监考教师数
    available_teachers: int = 0       # 可用于监考该科目的教师数
    special_teachers: int = 0         # 有特殊要求的教师数
    overlap_count: int = 0            # 与其他科目时间重叠的数量

    @classmethod
    def from_excel_row(cls, row):
        """
        从Excel表格的一行数据创建科目对象
        
        参数:
            row: Excel表格中的一行数据
        返回:
            Subject: 创建的科目对象
        """
        return cls(
            code=str(row['课程代码']),
            name=str(row['课程名称']),
            start_time=pd.to_datetime(row['开始时间']),
            end_time=pd.to_datetime(row['结束时间'])
        )

@dataclass
class Room:
    """教室类：存储考场信息和各科目所需的监考教师数"""
    name: str                   # 考场名称
    subject_requirements: Dict[str, int]  # 存储各科目所需教师数量的字典

    @classmethod
    def from_excel_row(cls, row, subject_columns):
        """
        从Excel表格的一行数据创建教室对象
        
        参数:
            row: Excel表格中的一行数据
            subject_columns: 科目列名列表
        返回:
            Room: 创建的教室对象
        """
        # 创建科目需求字典：只包含需要监考教师的科目
        requirements = {}
        for subject in subject_columns:
            if pd.notna(row[subject]) and int(row[subject]) > 0:
                requirements[subject] = int(row[subject])
        return cls(
            name=str(row['考场']),
            subject_requirements=requirements
        )

class Schedule:
    """调度方案类：存储和管理监考安排方案"""
    def __init__(self):
        # 存储监考安排：(科目,考场) -> [教师列表]
        self.assignments: Dict[Tuple[str, str], List[str]] = {}
        # 存储教师的监考安排：教师 -> [(科目,考场)]
        self.teacher_assignments: Dict[str, List[Tuple[str, str]]] = {}
        # 存储方案的适应度分数
        self.fitness_score: float = 0.0

    def add_assignment(self, subject: str, room: str, teacher: str):
        """
        添加一个监考安排
        
        参数:
            subject: 科目名称
            room: 考场名称
            teacher: 教师名称
        """
        key = (subject, room)
        if key not in self.assignments:
            self.assignments[key] = []
        self.assignments[key].append(teacher)
        
        if teacher not in self.teacher_assignments:
            self.teacher_assignments[teacher] = []
        self.teacher_assignments[teacher].append(key)

    def calculate_fitness(self, scheduler: 'ExamScheduler') -> float:
        """
        计算调度方案的适应度分数
        
        参数:
            scheduler: 考试调度器对象
        返回:
            float: 适应度分数
        """
        penalty = 0
        
        # 1. 检查硬约束条件
        ## 1.1 检查考场人数约束
        for room in scheduler.rooms:
            for subject, required in room.subject_requirements.items():
                actual = len(self.assignments.get((subject, room.name), []))
                if actual != required:
                    penalty += 300 * abs(actual - required)
                
                # 检查同考场监考教师的任教科目
                if actual > 1:
                    teachers = self.assignments.get((subject, room.name), [])
                    teaching_subjects = set()
                    for teacher_name in teachers:
                        teacher = next((t for t in scheduler.teachers if t.name == teacher_name), None)
                        if teacher and teacher.teaching_subject:
                            if teacher.teaching_subject in teaching_subjects:
                                penalty += 300
                            teaching_subjects.add(teacher.teaching_subject)
        
        ## 1.2 检查教师约束
        teacher_sessions = defaultdict(int)
        for teacher in scheduler.teachers:
            assignments = self.teacher_assignments.get(teacher.name, [])
            teacher_sessions[teacher.name] = len(assignments)
            
            # 检查场次限制
            if len(assignments) != teacher.max_sessions:
                penalty += 200 * abs(len(assignments) - teacher.max_sessions)
            
            # 检查必监考科目
            assigned_subjects = {subject for subject, _ in assignments}
            for must_subject in teacher.must_subjects:
                if must_subject not in assigned_subjects:
                    penalty += 200
            
            # 检查禁止监考科目
            for subject, _ in assignments:
                if subject in teacher.forbidden_subjects:
                    penalty += 200
        
        ## 1.3 检查时间冲突
        subject_times = {s.name: (s.start_time, s.end_time) for s in scheduler.subjects}
        for teacher, assignments in self.teacher_assignments.items():
            time_slots = []
            for subject, _ in assignments:
                start, end = subject_times[subject]
                time_slots.append((start, end))
            
            # 检查时间重叠
            for i in range(len(time_slots)):
                for j in range(i + 1, len(time_slots)):
                    if self._is_time_overlap(time_slots[i], time_slots[j]):
                        penalty += 300
        
        # 2. 检查软约束条件
        ## 2.1 检查考场偏好
        for teacher in scheduler.teachers:
            assignments = self.teacher_assignments.get(teacher.name, [])
            rooms = {room for _, room in assignments}
            
            # 检查必监考考场
            for must_room in teacher.must_rooms:
                if must_room not in rooms:
                    penalty += 50
            
            # 检查禁止监考考场
            for _, room in assignments:
                if room in teacher.forbidden_rooms:
                    penalty += 50
        
        ## 2.2 检查任教科目偏好
        for teacher in scheduler.teachers:
            if teacher.teaching_subject:
                assignments = self.teacher_assignments.get(teacher.name, [])
                teaching_subject_count = sum(
                    1 for subject, _ in assignments 
                    if subject == teacher.teaching_subject
                )
                if teaching_subject_count == 0:
                    penalty += 30
                else:
                    penalty -= teaching_subject_count * 10
        
        ## 2.3 检查时间分布
        for teacher, assignments in self.teacher_assignments.items():
            if not assignments:
                continue
            
            # 按日期和时间段分组
            time_slots_by_date = defaultdict(lambda: defaultdict(list))
            for subject, _ in assignments:
                start = subject_times[subject][0]
                date = start.date()
                period = self._get_time_period(start)
                time_slots_by_date[date][period].append(start)
            
            # 检查每天的时间分布
            for date, periods in time_slots_by_date.items():
                used_periods = len(periods)
                if used_periods > 2:
                    penalty += 150 * (used_periods - 2)
                
                # 检查同时段未安排的考试
                all_subjects_in_periods = defaultdict(set)
                for subject in scheduler.subjects:
                    if subject.start_time.date() == date:
                        period = self._get_time_period(subject.start_time)
                        all_subjects_in_periods[period].add(subject.name)
                
                for period, assigned_subjects in periods.items():
                    unassigned = all_subjects_in_periods[period] - set(assigned_subjects)
                    if unassigned:
                        penalty += 100 * len(unassigned)
        
        ## 2.4 检查工作量均衡性
        # 检查工作量均衡性
        if teacher_sessions:
            # 计算监考场次的标准差
            sessions = list(teacher_sessions.values())
            mean_sessions = sum(sessions) / len(sessions)
            variance = sum((s - mean_sessions) ** 2 for s in sessions) / len(sessions)
            std_dev = variance ** 0.5
            
            # 根据标准差增加惩罚
            penalty += 100 * std_dev
            
            # 检查最大最小场次差异
            max_sessions = max(sessions)
            min_sessions = min(sessions)
            if max_sessions - min_sessions > 2:
                penalty += 50 * (max_sessions - min_sessions - 2)
        
        # 计算最终的适应度分数
        self.fitness_score = 1000000 / (penalty + 1)
        return self.fitness_score

    @staticmethod
    def _is_time_overlap(slot1: Tuple[datetime, datetime],
                        slot2: Tuple[datetime, datetime]) -> bool:
        """
        检查两个时间段是否重叠
        
        参数:
            slot1: 第一个时间段(开始时间,结束时间)
            slot2: 第二个时间段(开始时间,结束时间)
        返回:
            bool: 是否重叠
        """
        start1, end1 = slot1
        start2, end2 = slot2
        return max(start1, start2) < min(end1, end2)

    @staticmethod
    def _get_time_period(time: datetime) -> str:
        """
        获取时间段分类（上午/下午/晚上）
        
        参数:
            time: 时间点
        返回:
            str: 时间段分类
        """
        hour = time.hour
        if hour < 12:
            return 'AM'
        elif hour < 18:
            return 'PM'
        return 'EVE'

class ExamScheduler:
    """考试监考安排系统"""
    def __init__(self, excel_file: str):
        """
        初始化考试监考安排系统
        
        参数:
            excel_file: Excel文件路径
        """
        self.excel_file = excel_file
        self.teachers: List[Teacher] = []  # 教师列表
        self.subjects: List[Subject] = []  # 科目列表
        self.original_subjects: List[Subject] = []  # 原始顺序的科目列表
        self.rooms: List[Room] = []  # 教室列表
        self.assignments: Dict[Tuple[str, str, str], List[str]] = {}  # 监考安排
        self.best_schedule: Schedule = None  # 最优调度方案
        self.current_schedule: Schedule = None  # 当前调度方案
        
        # 遗传算法参数设置
        self.population_size = 150  # 种群大小
        self.generations = 100  # 迭代代数
        self.mutation_rate = 0.3  # 变异率
        self.crossover_rate = 0.7  # 交叉率
        self.elite_size = 10  # 精英数量
        self.tournament_size = 5  # 锦标赛选择规模
        
        # 科目名称到科目对象的映射
        self.subject_map: Dict[str, Subject] = {}
        
        # 加载数据
        self._load_data()

    def _load_data(self):
        """从Excel文件加载数据"""
        try:
            # 读取Excel文件的三个工作表
            teacher_df = pd.read_excel(self.excel_file, sheet_name='监考员设置')
            subject_df = pd.read_excel(self.excel_file, sheet_name='考试科目设置')
            room_df = pd.read_excel(self.excel_file, sheet_name='考场设置')
            
            # 处理教师数据
            for _, row in teacher_df.iterrows():
                teacher = Teacher.from_excel_row(row)
                self.teachers.append(teacher)
            
            # 处理科目数据
            for _, row in subject_df.iterrows():
                subject = Subject.from_excel_row(row)
                self.subjects.append(subject)
                self.original_subjects.append(subject)
                self.subject_map[subject.name] = subject
            
            # 处理考场数据
            subject_columns = [col for col in room_df.columns if col != '考场']
            for _, row in room_df.iterrows():
                room = Room.from_excel_row(row, subject_columns)
                self.rooms.append(room)
                
            logging.info(f"成功加载数据: {len(self.teachers)}位教师, "
                        f"{len(self.subjects)}个科目, {len(self.rooms)}个考场")
            
        except Exception as e:
            logging.error(f"加载数据时出错: {str(e)}")
            raise

    def preprocess_teachers(self):
        """教师信息预处理：计算教师权重并排序"""
        # 获取所有科目集合
        all_subjects = {subject.name for subject in self.subjects}
        
        for teacher in self.teachers:
            # 设置教师可监考的科目
            teacher.available_subjects = all_subjects - teacher.forbidden_subjects
            if teacher.must_subjects:
                teacher.available_subjects.update(teacher.must_subjects)
        
        # 按多个条件对教师进行排序
        self.teachers.sort(key=lambda t: (
            t.calculate_weight(),  # 首先按综合权重排序
            len(t.must_subjects),  # 其次是必监考科目数
            len(t.must_rooms),     # 然后是必监考考场数
            -t.current_sessions,   # 当前安排较少的优先
            t.special_settings_count  # 最后是特殊设置数
        ), reverse=True)
        
        logging.info("完成教师信息预处理和排序")

    def preprocess_subjects(self):
        """科目安排顺序预处理：计算科目优先级并排序"""
        for subject in self.subjects:
            # 计算每个科目需要的总监考人数
            total_required = sum(
                room.subject_requirements.get(subject.name, 0)
                for room in self.rooms
            )
            subject.total_required_teachers = total_required
            
            # 计算可用教师数
            available_teachers = sum(
                1 for teacher in self.teachers
                if subject.name in teacher.available_subjects
            )
            subject.available_teachers = available_teachers
            
            # 计算特殊要求教师数
            subject.special_teachers = sum(
                1 for teacher in self.teachers
                if subject.name in teacher.must_subjects
            )
            
            # 计算与其他科目的时间重叠数
            subject.overlap_count = sum(
                1 for other in self.subjects
                if other != subject and self._is_time_overlap(
                    (subject.start_time, subject.end_time),
                    (other.start_time, other.end_time)
                )
            )

        # 定义科目排序关键字函数
        def subject_sort_key(s):
            return (
                s.special_teachers,  # 特殊要求教师数最多的优先
                s.total_required_teachers,  # 需求教师数多的优先
                -s.available_teachers,  # 可用教师少的优先
                s.overlap_count,  # 时间重叠多的优先
                (s.end_time - s.start_time).total_seconds()  # 考试时间长的优先
            )
        
        # 对科目进行排序
        self.subjects.sort(key=subject_sort_key, reverse=True)
        logging.info("完成科目安排顺序预处理")

    def _initialize_schedule(self) -> Schedule:
        """
        使用改进的贪心算法生成初始解
        
        返回:
            Schedule: 初始调度方案
        """
        schedule = Schedule()
        
        # 重置所有教师的状态
        for teacher in self.teachers:
            teacher.current_sessions = 0
            teacher.assigned_times = []
        
        # 按预处理后的顺序处理科目
        for subject in self.subjects:
            # 获取需要监考教师的考场列表
            room_requirements = [
                (room, room.subject_requirements.get(subject.name, 0))
                for room in self.rooms
                if subject.name in room.subject_requirements
            ]
            
            # 处理必监考教师
            assigned_teachers = set()
            for room, required_count in room_requirements:
                # 找出必须监考的教师
                must_teachers = [
                    teacher for teacher in self.teachers
                    if (teacher.name not in assigned_teachers and
                        (subject.name in teacher.must_subjects or room.name in teacher.must_rooms) and
                        subject.name in teacher.available_subjects and
                        teacher.current_sessions < teacher.max_sessions and
                        not self._has_time_conflict(schedule, teacher, subject))
                ]
                
                # 按优先级排序
                must_teachers.sort(key=lambda t: (
                    t.calculate_weight(),
                    -t.current_sessions,
                    -self._calculate_time_dispersion(t, subject)
                ), reverse=True)
                
                # 分配必监考教师
                for teacher in must_teachers[:required_count]:
                    schedule.add_assignment(subject.name, room.name, teacher.name)
                    teacher.current_sessions += 1
                    assigned_teachers.add(teacher.name)
                    teacher.assigned_times.append((subject.start_time, subject.end_time))
            
            # 处理剩余需求
            for room, required_count in room_requirements:
                current_count = len(schedule.assignments.get((subject.name, room.name), []))
                remaining_count = required_count - current_count
                
                if remaining_count > 0:
                    # 获取可用教师
                    available_teachers = [
                        teacher for teacher in self.teachers
                        if (teacher.name not in assigned_teachers and
                            subject.name in teacher.available_subjects and
                            room.name not in teacher.forbidden_rooms and
                            teacher.current_sessions < teacher.max_sessions and
                            not self._has_time_conflict(schedule, teacher, subject))
                    ]
                    
                    # 按优先级排序
                    available_teachers.sort(key=lambda t: (
                        -t.calculate_weight(),
                        t.current_sessions,
                        self._calculate_time_dispersion(t, subject)
                    ))
                    
                    # 分配教师
                    for teacher in available_teachers[:remaining_count]:
                        schedule.add_assignment(subject.name, room.name, teacher.name)
                        teacher.current_sessions += 1
                        assigned_teachers.add(teacher.name)
                        teacher.assigned_times.append((subject.start_time, subject.end_time))
        
        return schedule

    def _calculate_time_dispersion(self, teacher: Teacher, subject: Subject) -> float:
        """
        计算教师的时间分散度
        
        参数:
            teacher: 教师对象
            subject: 科目对象
        返回:
            float: 时间分散度分数
        """
        if not hasattr(teacher, 'assigned_times'):
            teacher.assigned_times = []
        
        if not teacher.assigned_times:
            return 0.0
        
        # 计算与现有安排的时间间隔
        time_gaps = []
        for start, end in teacher.assigned_times:
            gap = abs((subject.start_time - end).total_seconds())
            time_gaps.append(gap)
        
        # 返回最小时间间隔
        return min(time_gaps) if time_gaps else float('inf')

    def _has_time_conflict(self, schedule: Schedule, teacher: Teacher, subject: Subject) -> bool:
        """
        检查教师是否与当前科目有时间冲突
        
        参数:
            schedule: 调度方案对象
            teacher: 教师对象
            subject: 科目对象
        返回:
            bool: 是否存在时间冲突
        """
        return any(
            schedule._is_time_overlap(
                (subject.start_time, subject.end_time),
                (self.subject_map[s].start_time, self.subject_map[s].end_time)
            )
            for s, _ in schedule.teacher_assignments.get(teacher.name, [])
        )

    def schedule_exams(self) -> Schedule:
        """
        执行监考安排算法
        
        返回:
            Schedule: 最优的监考安排方案
        """
        logging.info("开始监考安排...")
        
        # 第一阶段：使用贪心算法生成初始解
        self.current_schedule = self._initialize_schedule()
        self.best_schedule = self.current_schedule
        best_fitness = self.current_schedule.calculate_fitness(self)
        
        logging.info(f"初始解适应度分数: {best_fitness}")
        
        # 如果初始解已经很好，直接返回
        if best_fitness > 900000:
            logging.info("初始解已经满足要求，无需进一步优化")
            return self.best_schedule
        
        # 第二阶段：使用遗传算法优化
        optimal_workers = min(self.population_size, os.cpu_count() * 2)
        population = self._initialize_population()
        generation = 0
        stagnation_counter = 0
        last_best_fitness = best_fitness
        
        while generation < self.generations:
            # 并行评估种群适应度
            with ThreadPoolExecutor(max_workers=optimal_workers) as executor:
                fitness_futures = {
                    executor.submit(schedule.calculate_fitness, self): schedule 
                    for schedule in population
                }
                
                fitness_results = []
                for future in as_completed(fitness_futures):
                    schedule = fitness_futures[future]
                    try:
                        fitness = future.result()
                        fitness_results.append((schedule, fitness))
                        if fitness > best_fitness:
                            best_fitness = fitness
                            self.best_schedule = schedule
                            logging.info(f"第{generation}代找到更好的解，适应度分数: {best_fitness}")
                            stagnation_counter = 0
                    except Exception as e:
                        logging.error(f"计算适应度时出错: {str(e)}")
            
            # 检查进化是否停滞
            if best_fitness <= last_best_fitness:
                stagnation_counter += 1
            else:
                stagnation_counter = 0
            last_best_fitness = best_fitness
            
            # 如果停滞超过20代，增加变异率
            if stagnation_counter > 20:
                self.mutation_rate = min(0.5, self.mutation_rate + 0.05)
                stagnation_counter = 0
                logging.info(f"优化停滞，提高变异率至: {self.mutation_rate}")
            
            # 如果找到足够好的解，提前结束
            if best_fitness > 950000:
                break
            
            # 选择精英个体
            population.sort(key=lambda s: s.fitness_score, reverse=True)
            new_population = population[:self.elite_size]
            
            # 并行生成新一代
            with ThreadPoolExecutor(max_workers=optimal_workers) as executor:
                offspring_futures = []
                while len(new_population) + len(offspring_futures) < self.population_size:
                    if random.random() < self.crossover_rate:
                        # 执行交叉操作
                        parent1 = self._tournament_select(population)
                        parent2 = self._tournament_select(population)
                        future = executor.submit(self._crossover, parent1, parent2)
                    else:
                        # 执行变异操作
                        parent = self._tournament_select(population)
                        future = executor.submit(self._mutate, parent)
                    offspring_futures.append(future)
                
                # 收集新一代个体
                for future in as_completed(offspring_futures):
                    try:
                        child = future.result()
                        new_population.append(child)
                    except Exception as e:
                        logging.error(f"生成新个体时出错: {str(e)}")
            
            population = new_population
            generation += 1
        
        # 第三阶段：局部搜索优化
        if best_fitness < 980000:
            logging.info("开始局部搜索优化...")
            improved_schedule = self._parallel_local_search(self.best_schedule, optimal_workers)
            improved_fitness = improved_schedule.calculate_fitness(self)
            
            if improved_fitness > best_fitness:
                self.best_schedule = improved_schedule
                logging.info(f"局部搜索找到更好的解，适应度分数: {improved_fitness}")
        
        return self.best_schedule

    def _parallel_local_search(self, schedule: Schedule, max_workers: int) -> Schedule:
        """
        并行局部搜索优化
        
        参数:
            schedule: 初始调度方案
            max_workers: 最大并行工作线程数
        返回:
            Schedule: 优化后的调度方案
        """
        current = schedule
        current_fitness = current.calculate_fitness(self)
        improved = True
        iterations = 0
        max_iterations = 1000
        no_improvement_count = 0
        small_improvement_count = 0
        best_fitness = current_fitness

        while improved and iterations < max_iterations:
            improved = False
            
            # 计算当前工作量分布和教师科目分配
            teacher_workload = defaultdict(int)
            teacher_subjects = defaultdict(set)
            for (subject, room), teachers in current.assignments.items():
                for teacher in teachers:
                    teacher_workload[teacher] += 1
                    teacher_subjects[teacher].add(subject)
            
            # 智能筛选交换组合
            swap_combinations = []
            for (subject1, room1), teachers1 in current.assignments.items():
                for (subject2, room2), teachers2 in current.assignments.items():
                    if subject1 == subject2 and room1 == room2:
                        continue
                    
                    # 获取时间信息
                    subject1_time = self.subject_map[subject1]
                    subject2_time = self.subject_map[subject2]
                    
                    # 检查时间冲突
                    if self._is_time_overlap(
                        (subject1_time.start_time, subject1_time.end_time),
                        (subject2_time.start_time, subject2_time.end_time)
                    ):
                        continue
                    
                    for t1 in teachers1:
                        teacher1_obj = next((t for t in self.teachers if t.name == t1), None)
                        if not teacher1_obj:
                            continue
                            
                        for t2 in teachers2:
                            teacher2_obj = next((t for t in self.teachers if t.name == t2), None)
                            if not teacher2_obj:
                                continue
                            
                            # 快速过滤无效交换
                            if any([
                                abs(teacher_workload[t1] - teacher_workload[t2]) <= 1,
                                subject1 in teacher2_obj.forbidden_subjects,
                                subject2 in teacher1_obj.forbidden_subjects,
                                room1 in teacher2_obj.forbidden_rooms,
                                room2 in teacher1_obj.forbidden_rooms
                            ]):
                                continue
                            
                            # 评估交换的潜在收益
                            potential_improvement = (
                                abs(teacher_workload[t1] - teacher_workload[t2]) * 2 +
                                (3 if subject1 in teacher2_obj.must_subjects else 0) +
                                (3 if subject2 in teacher1_obj.must_subjects else 0) +
                                (2 if room1 in teacher2_obj.must_rooms else 0) +
                                (2 if room2 in teacher1_obj.must_rooms else 0) +
                                (1 if subject1 == teacher2_obj.teaching_subject else 0) +
                                (1 if subject2 == teacher1_obj.teaching_subject else 0)
                            )
                            
                            if potential_improvement >= 3:
                                swap_combinations.append((subject1, room1, t1, subject2, room2, t2))
            
            # 分批处理交换评估
            batch_size = 50
            random.shuffle(swap_combinations)
            
            for i in range(0, len(swap_combinations), batch_size):
                batch = swap_combinations[i:i + batch_size]
                
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = []
                    for swap in batch:
                        future = executor.submit(
                            self._evaluate_swap,
                            current,
                            swap[0], swap[1], swap[2],
                            swap[3], swap[4], swap[5]
                        )
                        futures.append((future, swap))
                    
                    # 收集批次结果
                    for future, swap in futures:
                        try:
                            new_schedule, new_fitness = future.result()
                            if new_fitness > current_fitness:
                                current = new_schedule
                                current_fitness = new_fitness
                                improved = True
                                
                                if new_fitness > best_fitness:
                                    best_fitness = new_fitness
                                    small_improvement_count = 0
                                else:
                                    if (new_fitness - current_fitness) < 0.001:
                                        small_improvement_count += 1
                                break
                        except Exception as e:
                            logging.error(f"评估交换时出错: {str(e)}")
                    
                    if improved:
                        break
            
            # 优化终止条件检查
            if not improved:
                no_improvement_count += 1
            else:
                no_improvement_count = 0
            
            if any([
                no_improvement_count > 20,
                small_improvement_count > 10,
                current_fitness > 990000,
                iterations > max_iterations
            ]):
                break
            
            iterations += 1
            if iterations % 50 == 0:
                logging.info(f"局部搜索迭代次数: {iterations}, 当前适应度: {current_fitness}")
        
        logging.info(f"局部搜索完成，共迭代 {iterations} 次，最终适应度: {current_fitness}")
        return current

    def _evaluate_swap(self, schedule: Schedule, 
                      subject1: str, room1: str, teacher1: str,
                      subject2: str, room2: str, teacher2: str) -> Tuple[Schedule, float]:
        """
        评估教师交换的效果
        
        参数:
            schedule: 当前调度方案
            subject1, room1, teacher1: 第一个交换位置的信息
            subject2, room2, teacher2: 第二个交换位置的信息
        返回:
            Tuple[Schedule, float]: (新的调度方案, 适应度分数)
        """
        # 快速预检查
        if not self._is_valid_swap(subject1, room1, teacher1, subject2, room2, teacher2):
            return schedule, float('-inf')
        
        new_schedule = Schedule()
        
        # 只复制受影响的分配
        affected_assignments = {(subject1, room1), (subject2, room2)}
        for (s, r), teachers in schedule.assignments.items():
            if (s, r) in affected_assignments:
                if (s, r) == (subject1, room1):
                    new_teachers = [teacher2 if t == teacher1 else t for t in teachers]
                else:  # (s, r) == (subject2, room2)
                    new_teachers = [teacher1 if t == teacher2 else t for t in teachers]
            else:
                new_teachers = teachers
            
            for teacher in new_teachers:
                new_schedule.add_assignment(s, r, teacher)
        
        fitness = new_schedule.calculate_fitness(self)
        return new_schedule, fitness

    def _is_valid_swap(self, subject1: str, room1: str, teacher1: str,
                      subject2: str, room2: str, teacher2: str) -> bool:
        """
        快速检查交换是否有效
        
        参数:
            subject1, room1, teacher1: 第一个交换位置的信息
            subject2, room2, teacher2: 第二个交换位置的信息
        返回:
            bool: 交换是否有效
        """
        # 获取教师对象
        teacher1_obj = next((t for t in self.teachers if t.name == teacher1), None)
        teacher2_obj = next((t for t in self.teachers if t.name == teacher2), None)
        
        if not teacher1_obj or not teacher2_obj:
            return False
        
        # 检查基本约束
        if any([
            subject1 in teacher2_obj.forbidden_subjects,
            subject2 in teacher1_obj.forbidden_subjects,
            room1 in teacher2_obj.forbidden_rooms,
            room2 in teacher1_obj.forbidden_rooms
        ]):
            return False
        
        # 检查时间冲突
        subject1_time = self.subject_map[subject1]
        subject2_time = self.subject_map[subject2]
        if self._is_time_overlap(
            (subject1_time.start_time, subject1_time.end_time),
            (subject2_time.start_time, subject2_time.end_time)
        ):
            return False
        
        return True

    def _initialize_population(self) -> List[Schedule]:
        """
        使用并行处理初始化种群
        
        返回:
            List[Schedule]: 初始种群
        """
        with ThreadPoolExecutor() as executor:
            population = list(executor.map(lambda _: self._initialize_schedule(), 
                                        range(self.population_size)))
        return population

    def _tournament_select(self, population: List[Schedule]) -> Schedule:
        """
        改进的锦标赛选择
        
        参数:
            population: 当前种群
        返回:
            Schedule: 选中的个体
        """
        tournament = random.sample(population, self.tournament_size)
        tournament.sort(key=lambda s: s.fitness_score, reverse=True)
        # 以一定概率选择次优解
        if random.random() < 0.1 and len(tournament) > 1:
            return tournament[1]
        return tournament[0]

    def _crossover(self, parent1: Schedule, parent2: Schedule) -> Schedule:
        """
        交叉操作
        
        参数:
            parent1, parent2: 父代个体
        返回:
            Schedule: 子代个体
        """
        child = Schedule()
        
        # 获取所有分配
        all_assignments = list(set(parent1.assignments.keys()) | set(parent2.assignments.keys()))
        random.shuffle(all_assignments)
        
        # 从两个父代中随机选择分配
        for subject, room in all_assignments:
            if random.random() < 0.5 and (subject, room) in parent1.assignments:
                teachers = parent1.assignments[(subject, room)]
            elif (subject, room) in parent2.assignments:
                teachers = parent2.assignments[(subject, room)]
            else:
                continue
            
            for teacher in teachers:
                child.add_assignment(subject, room, teacher)
        
        return child

    def _mutate(self, schedule: Schedule) -> Schedule:
        """
        变异操作
        
        参数:
            schedule: 待变异的调度方案
        返回:
            Schedule: 变异后的调度方案
        """
        new_schedule = Schedule()
        
        # 重置所有教师的当前场次
        for teacher in self.teachers:
            teacher.current_sessions = 0
            
        # 计算当前教师工作量
        teacher_workload = defaultdict(int)
        for (subject, room), teachers in schedule.assignments.items():
            for teacher in teachers:
                teacher_workload[teacher] += 1
        
        # 复制大部分分配
        for (subject, room), teachers in schedule.assignments.items():
            if random.random() > self.mutation_rate:
                for teacher in teachers:
                    new_schedule.add_assignment(subject, room, teacher)
                    self._update_teacher_sessions(teacher, 1)
                continue
            
            # 对部分分配进行变异
            required_count = next(
                (r.subject_requirements[subject] for r in self.rooms if r.name == room),
                len(teachers)
            )
            # 获取可用教师，优先考虑任教科目匹配的教师
            available_teachers = [
                t for t in self.teachers
                if (subject in t.available_subjects and
                    room not in t.forbidden_rooms and
                    t.current_sessions < t.max_sessions)
            ]
            
            # 根据任教科目和工作量调整选择权重
            teacher_weights = []
            for teacher in available_teachers:
                weight = 1.0
                current_load = teacher_workload.get(teacher.name, 0)
                
                # 任教科目匹配时增加权重
                if teacher.teaching_subject == subject:
                    weight *= 2.0
                
                # 根据工作量调整权重
                if current_load >= max(teacher_workload.values()):
                    weight *= 0.5
                elif current_load <= min(teacher_workload.values()):
                    weight *= 1.5
                
                teacher_weights.append(weight)
            
            # 正规化权重
            if teacher_weights:
                total_weight = sum(teacher_weights)
                if total_weight > 0:
                    teacher_weights = [w/total_weight for w in teacher_weights]
                else:
                    teacher_weights = [1.0/len(teacher_weights)] * len(teacher_weights)
            
            # 根据权重随机选择教师
            selected_teachers = []
            for _ in range(min(required_count, len(available_teachers))):
                if not available_teachers:
                    break
                idx = random.choices(range(len(available_teachers)), weights=teacher_weights)[0]
                selected_teacher = available_teachers.pop(idx)
                teacher_weights.pop(idx)
                selected_teachers.append(selected_teacher)
                self._update_teacher_sessions(selected_teacher.name, 1)
            
            # 添加到新的调度方案
            for teacher in selected_teachers:
                new_schedule.add_assignment(subject, room, teacher.name)
        
        return new_schedule
        
    def _update_teacher_sessions(self, teacher_name: str, delta: int):
        """
        更新教师的当前场次
        
        参数:
            teacher_name: 教师姓名
            delta: 场次变化量
        """
        for teacher in self.teachers:
            if teacher.name == teacher_name:
                teacher.current_sessions += delta
                break

    def export_results(self, schedule: Schedule):
        """
        导出结果到Excel文件
        
        参数:
            schedule: 最终的调度方案
        """
        # 生成输出文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'监考安排_{timestamp}.xlsx'
        
        # 创建Excel写入器
        with pd.ExcelWriter(output_file) as writer:
            # 1. 导出考场安排表
            self._export_room_assignments(schedule, writer)
            
            # 2. 导出监考员安排表
            self._export_teacher_assignments(schedule, writer)
            
            # 3. 导出未安排场次表
            self._export_unassigned_sessions(schedule, writer)
            
            # 4. 导出统计信息
            self._export_statistics(schedule, writer)
        
        logging.info(f"结果已导出到文件: {output_file}")

    def _export_room_assignments(self, schedule: Schedule, writer: pd.ExcelWriter):
        """导出考场安排表"""
        data = []
        for room in self.rooms:
            row = {'考场': room.name}
            for subject in self.original_subjects:
                teachers = schedule.assignments.get((subject.name, room.name), [])
                required_count = room.subject_requirements.get(subject.name, 0)
                actual_count = len(teachers)
                missing_count = required_count - actual_count
                
                if required_count > 0:
                    if actual_count > 0:
                        value = '|'.join(teachers)
                        if missing_count > 0:
                            value += f' 缺{missing_count}'
                        row[subject.name] = value
                    elif missing_count > 0:
                        row[subject.name] = f'缺{missing_count}'
                else:
                    row[subject.name] = ''
            data.append(row)
        
        df = pd.DataFrame(data)
        df.to_excel(writer, sheet_name='考场安排', index=False)

    def _export_teacher_assignments(self, schedule: Schedule, writer: pd.ExcelWriter):
        """导出监考员安排表"""
        data = []
        for teacher in self.teachers:
            assignments = schedule.teacher_assignments.get(teacher.name, [])
            row = {
                '监考老师': teacher.name,
                '必监考科目': ','.join(teacher.must_subjects),
                '不监考科目': ','.join(teacher.forbidden_subjects),
                '必监考考场': ','.join(teacher.must_rooms),
                '不监考考场': ','.join(teacher.forbidden_rooms),
                '场次限制': teacher.max_sessions,
                '实际安排次数': len(assignments),
                '未安排场次': max(0, teacher.max_sessions - len(assignments))
            }
            
            for subject in self.original_subjects:
                rooms = [room for subj, room in assignments if subj == subject.name]
                row[subject.name] = ','.join(rooms) if rooms else ''
            
            data.append(row)
        
        df = pd.DataFrame(data)
        
        columns = [
            '监考老师', '必监考科目', '不监考科目', '必监考考场', 
            '不监考考场', '场次限制', '实际安排次数', '未安排场次'
        ]
        columns.extend(subject.name for subject in self.original_subjects)
        
        df = df[columns]
        df.to_excel(writer, sheet_name='监考员安排', index=False)

    def _export_unassigned_sessions(self, schedule: Schedule, writer: pd.ExcelWriter):
        """导出未安排场次表"""
        data = []
        for teacher in self.teachers:
            assignments = schedule.teacher_assignments.get(teacher.name, [])
            unassigned = teacher.max_sessions - len(assignments)
            if unassigned > 0:
                data.append({
                    '监考老师': teacher.name,
                    '预期场次': teacher.max_sessions,
                    '实际场次': len(assignments),
                    '未安排场次': unassigned
                })
        
        if data:
            df = pd.DataFrame(data)
            df.to_excel(writer, sheet_name='未安排场次', index=False)

    def _export_statistics(self, schedule: Schedule, writer: pd.ExcelWriter):
        """导出统计信息"""
        # 计算统计数据
        total_required = sum(
            sum(room.subject_requirements.values())
            for room in self.rooms
        )
        
        total_assigned = sum(
            len(teachers)
            for teachers in schedule.assignments.values()
        )
        
        completion_rate = (total_assigned / total_required * 100) if total_required > 0 else 0
        
        data = [{
            '统计项': '教师总数',
            '数值': len(self.teachers)
        }, {
            '统计项': '科目总数',
            '数值': len(self.subjects)
        }, {
            '统计项': '教室总数',
            '数值': len(self.rooms)
        }, {
            '统计项': '需求场次',
            '数值': total_required
        }, {
            '统计项': '实际安排场次',
            '数值': total_assigned
        }, {
            '统计项': '完成率',
            '数值': f'{completion_rate:.2f}%'
        }]
        
        df = pd.DataFrame(data)
        df.to_excel(writer, sheet_name='统计信息', index=False)

    def _is_time_overlap(self, slot1: Tuple[datetime, datetime],
                        slot2: Tuple[datetime, datetime]) -> bool:
        """检查两个时间段是否重叠"""
        start1, end1 = slot1
        start2, end2 = slot2
        return max(start1, start2) < min(end1, end2)

def main():
    """主函数"""
    try:
        # 初始化调度器
        scheduler = ExamScheduler("监考安排.xlsx")
        
        # 预处理
        scheduler.preprocess_teachers()
        scheduler.preprocess_subjects()
        
        # 执行监考安排
        best_schedule = scheduler.schedule_exams()
        
        # 导出结果
        scheduler.export_results(best_schedule)
        
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()