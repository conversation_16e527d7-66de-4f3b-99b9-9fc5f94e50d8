import pandas as pd

# 读取结果文件
df = pd.read_excel('监考安排_20250603_213212.xlsx', sheet_name='监考员安排')

# 查找丁继顺的分配情况
row = df[df['监考老师'] == '丁继顺']

if len(row) > 0:
    print('丁继顺的分配情况:')
    print(f'必监考科目: {row.iloc[0]["必监考科目"]}')
    print(f'必监考考场: {row.iloc[0]["必监考考场"]}')
    print(f'生物科目分配: {row.iloc[0]["生物"]}')
    print(f'约束检查: {row.iloc[0]["约束检查"]}')
    print(f'场次限制: {row.iloc[0]["场次限制"]}')
    print(f'实际安排次数: {row.iloc[0]["实际安排次数"]}')
    
    # 检查所有科目的分配
    print('\n所有科目分配情况:')
    subject_columns = ['数学', '语文', '英语', '物理', '化学', '生物', '政史', '地理']
    for subject in subject_columns:
        if subject in df.columns:
            assignment = row.iloc[0][subject]
            if pd.notna(assignment) and str(assignment).strip() != '':
                print(f'  {subject}: {assignment}')
else:
    print('未找到丁继顺的分配记录')

# 查看考场安排表中的生物科目15考场
print('\n=== 检查15考场的生物分配 ===')
df_rooms = pd.read_excel('监考安排_20250603_213212.xlsx', sheet_name='考场安排')
room15_row = df_rooms[df_rooms['考场'] == '15考场']
if len(room15_row) > 0:
    biology_assignment = room15_row.iloc[0]['生物']
    print(f'15考场生物分配: {biology_assignment}')
else:
    print('未找到15考场记录') 