{% extends "base.html" %}

{% block title %}{{ title }} - 监考安排系统{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background-color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        box-shadow: var(--card-shadow);
    }

    .form-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
    }

    .form-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        padding: 25px 20px;
        position: relative;
        overflow: hidden;
    }

    .form-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        bottom: -50%;
        left: -50%;
        background: linear-gradient(to bottom right, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
        transform: rotate(30deg);
    }

    .form-header h3 {
        position: relative;
        z-index: 1;
        margin: 0;
        font-weight: 600;
    }

    .form-body {
        padding: 30px;
    }

    .form-control:focus {
        border-color: var(--primary-light);
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    }

    .form-floating > label {
        color: var(--text-secondary);
    }

    .submit-btn {
        padding: 12px;
        font-weight: 500;
        letter-spacing: 0.5px;
        transition: all 0.3s;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .help-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
        height: 100%;
    }

    .help-header {
        background-color: rgba(var(--bs-info-rgb), 0.1);
        color: var(--primary-color);
        padding: 20px;
        border-bottom: none;
    }

    .help-body {
        padding: 25px;
    }

    .step-item {
        margin-bottom: 15px;
        position: relative;
        padding-left: 35px;
    }

    .step-number {
        position: absolute;
        left: 0;
        top: 0;
        width: 25px;
        height: 25px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .requirement-item {
        margin-bottom: 10px;
        position: relative;
        padding-left: 25px;
    }

    .requirement-item i {
        position: absolute;
        left: 0;
        top: 3px;
        color: var(--primary-color);
    }

    .file-upload-container {
        border: 2px dashed rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        padding: 30px 20px;
        text-align: center;
        transition: all 0.3s;
        cursor: pointer;
        margin-bottom: 20px;
    }

    .file-upload-container:hover {
        border-color: var(--primary-color);
    }

    .file-upload-icon {
        font-size: 3rem;
        color: var(--primary-light);
        margin-bottom: 15px;
    }

    .file-upload-text {
        margin-bottom: 10px;
    }

    .file-upload-input {
        display: none;
    }

    .file-preview {
        display: none;
        margin-top: 20px;
        padding: 15px;
        background-color: rgba(var(--bs-success-rgb), 0.1);
        border-radius: 8px;
    }

    .file-preview-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .file-preview-name {
        font-weight: 500;
        margin: 0;
    }

    .file-preview-size {
        color: var(--text-secondary);
        font-size: 0.85rem;
    }

    .file-preview-icon {
        font-size: 2rem;
        color: var(--primary-color);
        margin-right: 15px;
    }

    .file-preview-remove {
        color: var(--text-secondary);
        cursor: pointer;
        transition: color 0.2s;
    }

    .file-preview-remove:hover {
        color: var(--bs-danger);
    }

    .breadcrumb-custom {
        background-color: transparent;
        padding: 0;
        margin-bottom: 20px;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s;
    }

    .breadcrumb-item a:hover {
        color: var(--primary-light);
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--text-secondary);
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-custom">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt me-1"></i> 控制台
            </a></li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-plus-circle me-1"></i> 新建任务
            </li>
        </ol>
    </nav>
    <h2 class="mt-2 mb-0 fw-bold">创建监考安排任务</h2>
    <p class="text-muted">填写信息并上传Excel文件以创建新的监考安排任务</p>
</div>

<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="form-card">
            <div class="form-header text-white">
                <h3><i class="fas fa-clipboard-list me-2"></i> 任务信息</h3>
            </div>
            <div class="form-body">
                <form method="POST" action="{{ url_for('new_task') }}" enctype="multipart/form-data" id="taskForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-4">
                        <label for="title" class="form-label fw-medium">任务名称</label>
                        <input type="text" class="form-control form-control-lg" id="title" name="title" placeholder="输入任务名称" required>
                        <div class="form-text">请输入一个有意义的名称，便于后续识别</div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label fw-medium">任务描述（可选）</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="输入任务描述信息"></textarea>
                        <div class="form-text">添加描述信息可以帮助您区分不同的任务</div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label fw-medium">上传Excel文件</label>
                        <div class="file-upload-container" id="fileUploadContainer">
                            <div class="file-upload-icon">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <div id="uploadPrompt">
                                <h5 class="file-upload-text">拖放文件到此处或点击选择</h5>
                                <p class="text-muted small">支持 .xlsx 格式的Excel文件</p>
                                <button type="button" class="btn btn-outline-primary btn-sm">选择文件</button>
                            </div>
                            <div id="fileInfo" style="display: none;">
                                <h5 class="file-upload-text mb-2">已选择文件</h5>
                                <p class="file-name mb-3" id="selectedFileName"></p>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="changeFile">
                                    <i class="fas fa-exchange-alt me-1"></i>更换文件
                                </button>
                            </div>
                            <input class="file-upload-input" type="file" id="file" name="file" accept=".xlsx" required>
                        </div>

                        <!-- 验证进度和结果 -->
                        <div id="validationSection" class="mt-3" style="display: none;">
                            <div class="progress mb-2" style="height: 10px;">
                                <div id="validationProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="validationStatus" class="small text-muted"></div>

                            <!-- 验证结果 -->
                            <div id="validationResult" class="mt-3" style="display: none;">
                                <div id="validationSuccess" class="alert alert-success" style="display: none;">
                                    <i class="fas fa-check-circle me-2"></i> 文件验证通过！
                                    <div id="warningDetails" class="mt-2" style="display: none;">
                                        <h6 class="mb-2">警告信息：</h6>
                                        <ul class="mb-0" id="warningList"></ul>
                                    </div>
                                </div>
                                <div id="validationError" class="alert alert-danger" style="display: none;">
                                    <i class="fas fa-exclamation-circle me-2"></i> 文件验证失败！
                                    <div id="errorDetails" class="mt-2">
                                        <h6 class="mb-2">错误信息：</h6>
                                        <ol class="mb-0" id="errorList"></ol>
                                    </div>
                                    <div class="mt-2">
                                        <a id="downloadErrorReport" href="#" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-download me-1"></i> 下载错误报告
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg submit-btn" disabled>
                            <i class="fas fa-plus-circle me-2"></i> 创建任务
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="help-card">
            <div class="help-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> 使用说明</h5>
            </div>
            <div class="help-body">
                <h6 class="mb-3">操作步骤</h6>
                <div class="step-item">
                    <div class="step-number">1</div>
                    <p class="mb-1 fw-medium">填写任务名称</p>
                    <p class="text-muted small mb-0">输入一个有意义的名称，便于识别和管理</p>
                </div>

                <div class="step-item">
                    <div class="step-number">2</div>
                    <p class="mb-1 fw-medium">添加描述（可选）</p>
                    <p class="text-muted small mb-0">输入任务的详细描述信息</p>
                </div>

                <div class="step-item">
                    <div class="step-number">3</div>
                    <p class="mb-1 fw-medium">上传Excel文件</p>
                    <p class="text-muted small mb-0">选择并上传包含考试和教师信息的Excel文件</p>
                </div>

                <div class="step-item">
                    <div class="step-number">4</div>
                    <p class="mb-1 fw-medium">创建任务</p>
                    <p class="text-muted small mb-0">点击"创建任务"按钮提交表单</p>
                </div>

                <div class="step-item">
                    <div class="step-number">5</div>
                    <p class="mb-1 fw-medium">启动处理</p>
                    <p class="text-muted small mb-0">在任务详情页面启动监考安排处理</p>
                </div>

                <hr class="my-4">

                <h6 class="mb-3">Excel文件要求</h6>
                <div class="requirement-item">
                    <i class="fas fa-check-circle"></i>
                    <p class="mb-1">包含考试信息表</p>
                </div>
                <div class="requirement-item">
                    <i class="fas fa-check-circle"></i>
                    <p class="mb-1">包含教师信息表</p>
                </div>
                <div class="requirement-item">
                    <i class="fas fa-check-circle"></i>
                    <p class="mb-1">按照系统要求的格式填写</p>
                </div>
                <div class="requirement-item">
                    <i class="fas fa-check-circle"></i>
                    <p class="mb-1">文件大小不超过10MB</p>
                </div>

                <div class="alert alert-info mt-4 mb-0">
                    <i class="fas fa-lightbulb me-2"></i> 提示：您可以下载模板文件作为参考，或使用<a href="{{ url_for('validate_test') }}" class="alert-link">验证测试</a>功能提前测试文件。
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('file');
        const fileUploadContainer = document.getElementById('fileUploadContainer');
        const uploadPrompt = document.getElementById('uploadPrompt');
        const fileInfo = document.getElementById('fileInfo');
        const selectedFileName = document.getElementById('selectedFileName');
        const changeFileBtn = document.getElementById('changeFile');
        const submitBtn = document.querySelector('button[type="submit"]');
        
        let currentValidationId = null;
        let validationCompleted = false;

        // 点击上传区域触发文件选择
        fileUploadContainer.addEventListener('click', function(e) {
            if (e.target !== changeFileBtn) {
                fileInput.click();
            }
        });

        // 拖拽文件上传
        fileUploadContainer.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            fileUploadContainer.style.borderColor = 'var(--primary-color)';
            fileUploadContainer.style.backgroundColor = 'rgba(var(--primary-rgb), 0.05)';
        });

        fileUploadContainer.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            fileUploadContainer.style.borderColor = 'rgba(0, 0, 0, 0.1)';
            fileUploadContainer.style.backgroundColor = '';
        });

        fileUploadContainer.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length) {
                fileInput.files = files;
                handleFileSelect(files[0]);
            }

            fileUploadContainer.style.borderColor = 'rgba(0, 0, 0, 0.1)';
            fileUploadContainer.style.backgroundColor = '';
        });

        // 文件选择处理
        fileInput.addEventListener('change', function(e) {
            if (this.files.length > 0) {
                handleFileSelect(this.files[0]);
            }
        });

        // 更换文件按钮
        changeFileBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            fileInput.click();
        });

        // 处理文件选择
        function handleFileSelect(file) {
            // 检查文件类型
            const extension = file.name.split('.').pop().toLowerCase();
            if (extension !== 'xlsx') {
                showError('请选择.xlsx格式的Excel文件');
                fileInput.value = '';
                resetFileUpload();
                return;
            }

            // 检查文件大小（10MB限制）
            if (file.size > 10 * 1024 * 1024) {
                showError('文件大小不能超过10MB');
                fileInput.value = '';
                resetFileUpload();
                return;
            }

            // 更新界面显示
            selectedFileName.textContent = file.name;
            uploadPrompt.style.display = 'none';
            fileInfo.style.display = 'block';

            // 重置验证状态
            resetValidation();

            // 开始验证
            startValidation();
        }

        // 重置文件上传界面
        function resetFileUpload() {
            uploadPrompt.style.display = 'block';
            fileInfo.style.display = 'none';
            selectedFileName.textContent = '';
            resetValidation();
        }

        // 重置验证状态
        function resetValidation() {
            validationCompleted = false;
            currentValidationId = null;
            submitBtn.disabled = true;
            
            const validationSection = document.getElementById('validationSection');
            const validationResult = document.getElementById('validationResult');
            const validationSuccess = document.getElementById('validationSuccess');
            const validationError = document.getElementById('validationError');
            const validationProgress = document.getElementById('validationProgress');
            const validationStatus = document.getElementById('validationStatus');
            
            validationSection.style.display = 'none';
            validationResult.style.display = 'none';
            validationSuccess.style.display = 'none';
            validationError.style.display = 'none';
            validationProgress.style.width = '0%';
            validationStatus.textContent = '';
        }

        // 开始验证
        function startValidation() {
            if (!fileInput.files || !fileInput.files[0]) {
                showError('请先选择文件');
                return;
            }

            const validationSection = document.getElementById('validationSection');
            const validationProgress = document.getElementById('validationProgress');
            const validationStatus = document.getElementById('validationStatus');

            validationSection.style.display = 'block';
            validationProgress.style.width = '0%';
            validationStatus.textContent = '正在准备验证...';

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            fetch('/task/validate', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.validation_id) {
                    currentValidationId = data.validation_id;
                    checkValidationProgress();
                } else {
                    showError('验证过程出错');
                }
            })
            .catch(error => {
                showError('验证请求失败: ' + error);
            });
        }

        // 检查验证进度
        function checkValidationProgress() {
            if (!currentValidationId) return;

            const validationProgress = document.getElementById('validationProgress');
            const validationStatus = document.getElementById('validationStatus');

            fetch(`/task/validate/${currentValidationId}/progress`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                    return;
                }

                validationProgress.style.width = `${data.progress}%`;
                validationStatus.textContent = `验证进度: ${data.progress}%`;

                if (data.completed) {
                    showValidationResult(data.result);
                } else {
                    setTimeout(checkValidationProgress, 1000);
                }
            })
            .catch(error => {
                showError('获取验证进度失败: ' + error);
            });
        }

        // 显示验证结果
        function showValidationResult(result) {
            const validationResult = document.getElementById('validationResult');
            const validationSuccess = document.getElementById('validationSuccess');
            const validationError = document.getElementById('validationError');
            const errorList = document.getElementById('errorList');
            const warningList = document.getElementById('warningList');
            const warningDetails = document.getElementById('warningDetails');

            validationResult.style.display = 'block';
            errorList.innerHTML = '';
            warningList.innerHTML = '';

            if (result.is_valid) {
                // 验证通过
                validationSuccess.style.display = 'block';
                validationError.style.display = 'none';
                submitBtn.disabled = false;
                validationCompleted = true;

                // 显示警告信息（如果有）
                if (result.warnings && result.warnings.length > 0) {
                    warningDetails.style.display = 'block';
                    result.warnings.forEach(warning => {
                        const li = document.createElement('li');
                        li.textContent = warning;
                        warningList.appendChild(li);
                    });
                } else {
                    warningDetails.style.display = 'none';
                }
            } else {
                // 验证失败
                validationSuccess.style.display = 'none';
                validationError.style.display = 'block';
                submitBtn.disabled = true;
                validationCompleted = false;

                // 显示错误信息
                if (result.errors && result.errors.length > 0) {
                    const ol = document.createElement('ol');
                    ol.className = 'mb-0';

                    result.errors.forEach(error => {
                        const li = document.createElement('li');
                        li.textContent = error;
                        ol.appendChild(li);
                    });

                    errorList.appendChild(ol);
                }

                // 设置错误报告下载链接
                if (result.error_report) {
                    const downloadBtn = document.getElementById('downloadErrorReport');
                    downloadBtn.href = `/task/error-report/${currentValidationId}`;
                    downloadBtn.style.display = 'inline-block';
                }
            }
        }

        // 显示错误信息
        function showError(message) {
            const validationResult = document.getElementById('validationResult');
            const validationSuccess = document.getElementById('validationSuccess');
            const validationError = document.getElementById('validationError');
            const errorList = document.getElementById('errorList');

            validationResult.style.display = 'block';
            validationSuccess.style.display = 'none';
            validationError.style.display = 'block';
            errorList.innerHTML = `<li>${message}</li>`;
            submitBtn.disabled = true;
            validationCompleted = false;
        }

        // 表单提交处理
        document.getElementById('taskForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!validationCompleted) {
                showError('请等待文件验证完成');
                return;
            }

            const title = document.getElementById('title').value.trim();
            if (!title) {
                showError('请输入任务名称');
                return;
            }

            // 禁用提交按钮
            submitBtn.disabled = true;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 创建中...';

            try {
                const formData = new FormData();
                formData.append('title', title);
                formData.append('description', document.getElementById('description').value.trim());

                const response = await fetch(`/task/create-from-validated/${currentValidationId}`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    },
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // 使用返回的task_id构建URL并跳转
                    window.location.href = `/task/${data.task_id}`;
                } else {
                    if (response.status === 404) {
                        showError('验证结果已失效，请重新上传文件');
                        resetFileUpload();
                    } else {
                        showError(data.message || '创建任务失败');
                    }
                }
            } catch (error) {
                showError('创建任务时发生错误: ' + error);
            } finally {
                // 恢复提交按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });
    });
</script>
{% endblock %}
