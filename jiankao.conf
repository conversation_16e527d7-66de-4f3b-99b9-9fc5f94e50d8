[program:jiankao]
command=/path/to/jiankao/venv/bin/gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 120 --log-file=/path/to/jiankao/logs/gunicorn.log app:app
directory=/path/to/jiankao
user=www-data
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
stderr_logfile=/path/to/jiankao/logs/supervisor_err.log
stdout_logfile=/path/to/jiankao/logs/supervisor_out.log
environment=FLASK_APP=app.py,FLASK_ENV=production

[program:jiankao_cleanup]
command=/bin/bash -c "find /tmp/jiankao -type f -mtime +7 -delete"
user=www-data
autostart=true
autorestart=false
startsecs=0
startretries=0
exitcodes=0
stderr_logfile=/path/to/jiankao/logs/cleanup_err.log
stdout_logfile=/path/to/jiankao/logs/cleanup_out.log
schedule=0 3 * * *
