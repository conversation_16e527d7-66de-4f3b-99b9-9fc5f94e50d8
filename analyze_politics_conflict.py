#!/usr/bin/env python3
import pandas as pd

# 读取数据
result_file = '监考安排_20250602_160104.xlsx'
teacher_result = pd.read_excel(result_file, sheet_name='监考员安排')
room_result = pd.read_excel(result_file, sheet_name='考场安排')

print("=== 深入分析政史科目缺1的原因 ===")

# 1. 查看政史科目的总体安排情况
print("1. 政史科目的总体安排情况:")
politics_assignments = {}
for index, row in room_result.iterrows():
    room_name = row['考场']
    politics_teacher = str(row['政史'])
    if politics_teacher != 'nan' and politics_teacher.strip() != '':
        politics_assignments[room_name] = politics_teacher

print(f"  政史科目总考场数: 15")
print(f"  已安排政史监考的考场数: {len(politics_assignments)}")
print(f"  未安排(缺1)的考场数: {15 - len(politics_assignments)}")

print("\n  已安排的政史监考情况:")
for room, teacher in politics_assignments.items():
    print(f"    {room}: {teacher}")

print("\n  缺1的考场:")
for index, row in room_result.iterrows():
    room_name = row['考场']
    politics_teacher = str(row['政史'])
    if '缺1' in politics_teacher:
        print(f"    {room_name}: {politics_teacher}")

# 2. 分析政史科目相关的教师
print("\n2. 政史科目相关教师分析:")

# 找出可能监考政史的教师
politics_related_teachers = []

for index, row in teacher_result.iterrows():
    teacher_name = row['监考老师']
    teaching_subject = str(row.get('任教科目', ''))
    must_subjects = str(row.get('必监考科目', ''))
    forbidden_subjects = str(row.get('不监考科目', ''))
    
    # 检查是否与政史相关
    is_politics_related = False
    reason = []
    
    if '政治' in teaching_subject or '历史' in teaching_subject:
        is_politics_related = True
        reason.append(f"任教科目: {teaching_subject}")
    
    if '政史' in must_subjects or '政治' in must_subjects or '历史' in must_subjects:
        is_politics_related = True
        reason.append(f"必监考科目: {must_subjects}")
    
    if '政史' in forbidden_subjects or '政治' in forbidden_subjects or '历史' in forbidden_subjects:
        is_politics_related = True
        reason.append(f"禁止监考科目: {forbidden_subjects}")
    
    if is_politics_related:
        politics_related_teachers.append({
            'name': teacher_name,
            'reason': '; '.join(reason),
            'limit': row['场次限制'],
            'actual': row['实际安排次数'],
            'unassigned': row['未安排场次'],
            'politics_room': row.get('政史', '')
        })

print(f"  与政史相关的教师数量: {len(politics_related_teachers)}")
for teacher in politics_related_teachers:
    print(f"    {teacher['name']}: {teacher['reason']}")
    print(f"      场次: 限制{teacher['limit']}, 实际{teacher['actual']}, 未安排{teacher['unassigned']}")
    politics_room = str(teacher['politics_room'])
    if politics_room != 'nan' and politics_room.strip() != '':
        print(f"      政史安排: {politics_room}")
    else:
        print(f"      政史安排: 无")

# 3. 分析未安排政史的教师
print("\n3. 有未安排场次但没有安排政史的教师:")
unassigned_teachers = teacher_result[teacher_result['未安排场次'] > 0]

for index, row in unassigned_teachers.iterrows():
    teacher_name = row['监考老师']
    politics_room = str(row.get('政史', ''))
    
    if politics_room == 'nan' or politics_room.strip() == '':
        print(f"  {teacher_name}:")
        print(f"    场次: 限制{row['场次限制']}, 实际{row['实际安排次数']}, 未安排{row['未安排场次']}")
        
        # 检查是否有阻止其监考政史的约束
        forbidden_subjects = str(row.get('不监考科目', ''))
        must_rooms = str(row.get('必监考考场', ''))
        forbidden_rooms = str(row.get('不监考考场', ''))
        
        constraints = []
        if '政史' in forbidden_subjects or '政治' in forbidden_subjects or '历史' in forbidden_subjects:
            constraints.append(f"禁止监考政史相关科目: {forbidden_subjects}")
        if must_rooms != 'nan' and must_rooms.strip() != '':
            constraints.append(f"必监考考场限制: {must_rooms}")
        if forbidden_rooms != 'nan' and forbidden_rooms.strip() != '':
            constraints.append(f"不监考考场限制: {forbidden_rooms}")
        
        if constraints:
            print(f"    可能的阻止因素: {'; '.join(constraints)}")
        else:
            print(f"    无明显约束阻止其监考政史")

# 4. 时间冲突分析
print("\n4. 政史科目时间冲突分析:")
print("  政史考试时间: 2025-05-29 16:00:00 - 2025-05-29 18:00:00")

# 查看在政史时间段内已经有其他安排的教师
for index, row in unassigned_teachers.iterrows():
    teacher_name = row['监考老师']
    other_subjects = []
    
    for subject in ['语文', '数学', '英语', '理化']:
        room = str(row.get(subject, ''))
        if room != 'nan' and room.strip() != '':
            other_subjects.append(f"{subject}({room})")
    
    if other_subjects:
        print(f"  {teacher_name} 在其他时间段的安排: {', '.join(other_subjects)}") 