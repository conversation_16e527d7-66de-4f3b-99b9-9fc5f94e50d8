#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时长均衡功能测试脚本
"""

import pandas as pd
from datetime import datetime, timedelta
from main import ExamScheduler, Schedule, Teacher, Subject, Room

def create_test_data():
    """创建测试数据"""
    # 创建测试教师数据
    teachers_data = [
        {'序号': 1, '监考老师': '张老师', '必监考科目': '', '不监考科目': '', 
         '必监考考场': '', '不监考考场': '', '任教科目': '数学', '场次限制': 2},
        {'序号': 2, '监考老师': '李老师', '必监考科目': '', '不监考科目': '', 
         '必监考考场': '', '不监考考场': '', '任教科目': '语文', '场次限制': 2},
        {'序号': 3, '监考老师': '王老师', '必监考科目': '', '不监考科目': '', 
         '必监考考场': '', '不监考考场': '', '任教科目': '英语', '场次限制': 2},
        {'序号': 4, '监考老师': '赵老师', '必监考科目': '', '不监考科目': '', 
         '必监考考场': '', '不监考考场': '', '任教科目': '物理', '场次限制': 2}
    ]
    
    # 创建测试科目数据（不同时长）
    base_time = datetime(2024, 1, 15, 9, 0)
    subjects_data = [
        {'课程代码': 'MATH001', '课程名称': '数学', 
         '开始时间': base_time, '结束时间': base_time + timedelta(hours=2)},  # 2小时
        {'课程代码': 'CHIN001', '课程名称': '语文', 
         '开始时间': base_time + timedelta(hours=3), '结束时间': base_time + timedelta(hours=5.5)},  # 2.5小时
        {'课程代码': 'ENG001', '课程名称': '英语', 
         '开始时间': base_time + timedelta(days=1), '结束时间': base_time + timedelta(days=1, hours=1.5)},  # 1.5小时
        {'课程代码': 'PHY001', '课程名称': '物理', 
         '开始时间': base_time + timedelta(days=1, hours=3), '结束时间': base_time + timedelta(days=1, hours=6)}  # 3小时
    ]
    
    # 创建测试考场数据
    rooms_data = [
        {'考场': '101教室', '数学': 1, '语文': 1, '英语': 1, '物理': 1},
        {'考场': '102教室', '数学': 1, '语文': 1, '英语': 1, '物理': 1}
    ]
    
    return teachers_data, subjects_data, rooms_data

def test_duration_balance():
    """测试时长均衡功能"""
    print("开始测试时长均衡功能...")
    
    # 创建测试数据
    teachers_data, subjects_data, rooms_data = create_test_data()
    
    # 创建教师对象
    teachers = []
    for data in teachers_data:
        teacher = Teacher.from_excel_row(pd.Series(data))
        teachers.append(teacher)
    
    # 创建科目对象
    subjects = []
    subject_map = {}
    for data in subjects_data:
        subject = Subject.from_excel_row(pd.Series(data))
        subjects.append(subject)
        subject_map[subject.name] = subject
    
    # 创建考场对象
    rooms = []
    subject_columns = ['数学', '语文', '英语', '物理']
    for data in rooms_data:
        room = Room.from_excel_row(pd.Series(data), subject_columns)
        rooms.append(room)
    
    # 创建调度方案
    schedule = Schedule()
    
    # 手动创建一个不均衡的分配（用于测试）
    # 张老师：数学(2h) + 语文(2.5h) = 4.5h
    # 李老师：英语(1.5h) + 物理(3h) = 4.5h
    # 王老师：数学(2h) + 英语(1.5h) = 3.5h  
    # 赵老师：语文(2.5h) + 物理(3h) = 5.5h
    
    schedule.add_assignment('数学', '101教室', '张老师')
    schedule.add_assignment('语文', '101教室', '张老师')
    schedule.add_assignment('英语', '101教室', '李老师')
    schedule.add_assignment('物理', '101教室', '李老师')
    schedule.add_assignment('数学', '102教室', '王老师')
    schedule.add_assignment('英语', '102教室', '王老师')
    schedule.add_assignment('语文', '102教室', '赵老师')
    schedule.add_assignment('物理', '102教室', '赵老师')
    
    # 创建模拟的调度器对象
    class MockScheduler:
        def __init__(self):
            self.teachers = teachers
            self.subjects = subjects
            self.subject_map = subject_map
            self.rooms = rooms
    
    mock_scheduler = MockScheduler()
    
    # 测试时长均衡检查
    print("\n=== 测试时长均衡检查 ===")
    penalty = schedule._check_duration_balance_by_sessions(mock_scheduler)
    print(f"时长均衡惩罚分数: {penalty}")
    
    # 测试验证报告
    print("\n=== 测试验证报告 ===")
    mock_scheduler_with_methods = type('MockScheduler', (), {
        'teachers': teachers,
        'subjects': subjects,
        'subject_map': subject_map,
        'rooms': rooms,
        '_validate_duration_balance': lambda self, schedule: schedule._validate_duration_balance_mock(self)
    })()
    
    # 手动实现验证方法
    def validate_duration_balance_mock(scheduler):
        from collections import defaultdict
        session_groups = defaultdict(list)
        
        for teacher in scheduler.teachers:
            actual_sessions = len(schedule.teacher_assignments.get(teacher.name, []))
            if actual_sessions == teacher.max_sessions:
                total_duration = sum(
                    (scheduler.subject_map[s].end_time - scheduler.subject_map[s].start_time).total_seconds() / 60
                    for s, _ in schedule.teacher_assignments.get(teacher.name, [])
                )
                session_groups[actual_sessions].append({
                    'teacher': teacher.name,
                    'duration': total_duration
                })
        
        balance_report = {}
        for sessions, teachers_data in session_groups.items():
            if len(teachers_data) > 1:
                durations = [data['duration'] for data in teachers_data]
                max_dur = max(durations)
                min_dur = min(durations)
                diff = max_dur - min_dur
                
                balance_report[sessions] = {
                    'teacher_count': len(teachers_data),
                    'max_duration': max_dur,
                    'min_duration': min_dur,
                    'difference': diff,
                    'is_balanced': diff <= 120  # 2小时内为均衡
                }
        
        return balance_report
    
    balance_report = validate_duration_balance_mock(mock_scheduler)
    
    print("时长均衡验证报告:")
    for sessions, report in balance_report.items():
        status = "均衡" if report['is_balanced'] else "不均衡"
        print(f"  {sessions}场次组({report['teacher_count']}人): "
              f"最大{report['max_duration']:.1f}分钟, "
              f"最小{report['min_duration']:.1f}分钟, "
              f"差异{report['difference']:.1f}分钟 - {status}")
    
    # 计算每个教师的具体时长
    print("\n=== 教师时长详情 ===")
    for teacher in teachers:
        assignments = schedule.teacher_assignments.get(teacher.name, [])
        total_duration = sum(
            (subject_map[s].end_time - subject_map[s].start_time).total_seconds() / 60
            for s, _ in assignments
        )
        print(f"{teacher.name}: {len(assignments)}场次, {total_duration:.1f}分钟 ({total_duration/60:.2f}小时)")
        for subject, room in assignments:
            duration = (subject_map[subject].end_time - subject_map[subject].start_time).total_seconds() / 60
            print(f"  - {subject}@{room}: {duration:.1f}分钟")
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_duration_balance() 