#!/usr/bin/env python3
"""
动态约束权重调整模块

根据约束冲突严重程度自动调整权重，避免过度刚性的约束导致无解
"""

import logging
from collections import defaultdict, Counter

class DynamicConstraintManager:
    """动态约束管理器"""
    
    def __init__(self):
        # 基础权重配置
        self.base_weights = {
            'forbidden_subjects': 1000,    # 禁止监考科目
            'must_subjects': 1000,         # 必监考科目  
            'time_conflicts': 1000,        # 时间冲突
            'room_capacity': 1000,         # 考场容量
            'session_limits': 1000,        # 场次限制
            'room_preferences': 500,       # 考场偏好
            'subject_preferences': 200,    # 科目偏好
            'workload_balance': 100        # 工作量平衡
        }
        
        # 当前权重（可动态调整）
        self.current_weights = self.base_weights.copy()
        
        # 约束违反历史记录
        self.violation_history = defaultdict(list)
        
        # 调整历史
        self.adjustment_history = []
        
    def detect_constraint_conflicts(self, schedule, scheduler):
        """检测约束冲突情况"""
        conflicts = {
            'forbidden_subjects': 0,
            'must_subjects': 0, 
            'time_conflicts': 0,
            'room_capacity': 0,
            'session_limits': 0,
            'room_preferences': 0,
            'subject_preferences': 0,
            'workload_balance': 0
        }
        
        # 统计各类约束违反
        for teacher in scheduler.teachers:
            teacher_assignments = self._get_teacher_assignments(schedule, teacher)
            
            # 1. 场次限制违反
            if len(teacher_assignments) != teacher.session_limit:
                conflicts['session_limits'] += abs(len(teacher_assignments) - teacher.session_limit)
            
            # 2. 必监考科目违反
            if teacher.must_subjects:
                assigned_subjects = set(subj for subj, _ in teacher_assignments)
                missing_subjects = teacher.must_subjects - assigned_subjects
                conflicts['must_subjects'] += len(missing_subjects)
            
            # 3. 禁止监考科目违反
            if teacher.forbidden_subjects:
                assigned_subjects = set(subj for subj, _ in teacher_assignments)
                forbidden_assigned = teacher.forbidden_subjects & assigned_subjects
                conflicts['forbidden_subjects'] += len(forbidden_assigned)
        
        # 4. 考场缺人情况
        room_shortage = self._count_room_shortages(schedule, scheduler)
        conflicts['room_capacity'] += room_shortage
        
        return conflicts
    
    def _get_teacher_assignments(self, schedule, teacher):
        """获取教师的所有分配"""
        assignments = []
        for (subject, room_name), teachers in schedule.assignments.items():
            if teacher in teachers:
                assignments.append((subject, room_name))
        return assignments
    
    def _count_room_shortages(self, schedule, scheduler):
        """统计考场缺人数量"""
        shortage_count = 0
        for room in scheduler.rooms:
            for subject, required_count in room.subject_requirements.items():
                assigned_count = len(schedule.assignments.get((subject, room.name), []))
                if assigned_count < required_count:
                    shortage_count += (required_count - assigned_count)
        return shortage_count
    
    def calculate_conflict_severity(self, conflicts):
        """计算约束冲突严重程度"""
        # 加权严重程度
        severity_weights = {
            'forbidden_subjects': 3.0,    # 最严重
            'time_conflicts': 3.0,        # 最严重
            'must_subjects': 2.5,         # 很严重
            'room_capacity': 2.0,         # 严重
            'session_limits': 1.5,        # 中等严重
            'room_preferences': 1.0,      # 轻微
            'subject_preferences': 0.5,   # 最轻微
            'workload_balance': 0.3       # 最轻微
        }
        
        total_severity = 0
        for constraint, violation_count in conflicts.items():
            if violation_count > 0:
                weight = severity_weights.get(constraint, 1.0)
                severity = violation_count * weight
                total_severity += severity
                
        return total_severity
    
    def adjust_weights_adaptive(self, conflicts, fitness_score):
        """自适应调整约束权重"""
        
        print(f"\n=== 动态约束权重调整 ===")
        print(f"当前适应度分数: {fitness_score:.2f}")
        print(f"约束冲突检测: {conflicts}")
        
        adjustments_made = False
        
        # 记录违反历史
        for constraint, count in conflicts.items():
            self.violation_history[constraint].append(count)
        
        # 如果适应度分数很低（< 70），说明约束过于严格
        if fitness_score < 70:
            # 策略1: 降低冲突最严重的约束权重
            max_conflict_constraint = max(conflicts.items(), key=lambda x: x[1])
            if max_conflict_constraint[1] > 0:
                constraint_name = max_conflict_constraint[0]
                
                # 计算调整幅度（根据违反严重程度）
                violation_count = max_conflict_constraint[1]
                reduction_factor = min(0.8, 1.0 - violation_count * 0.1)  # 最多降低80%
                
                old_weight = self.current_weights[constraint_name]
                new_weight = max(100, old_weight * reduction_factor)  # 最低100分
                
                if new_weight != old_weight:
                    self.current_weights[constraint_name] = new_weight
                    adjustment_record = {
                        'constraint': constraint_name,
                        'old_weight': old_weight,
                        'new_weight': new_weight,
                        'reason': f'严重冲突({violation_count}次违反)',
                        'fitness_score': fitness_score
                    }
                    self.adjustment_history.append(adjustment_record)
                    
                    print(f"  调整 {constraint_name}: {old_weight} → {new_weight}")
                    print(f"  原因: {adjustment_record['reason']}")
                    adjustments_made = True
        
        # 策略2: 对持续违反的约束进行渐进式降权
        for constraint, history in self.violation_history.items():
            if len(history) >= 3:  # 连续3次违反
                recent_violations = history[-3:]
                if all(v > 0 for v in recent_violations):  # 持续违反
                    avg_violations = sum(recent_violations) / len(recent_violations)
                    
                    # 渐进式降权
                    reduction = min(0.9, 1.0 - avg_violations * 0.05)
                    old_weight = self.current_weights[constraint]
                    new_weight = max(50, old_weight * reduction)  # 最低50分
                    
                    if new_weight != old_weight:
                        self.current_weights[constraint] = new_weight
                        adjustment_record = {
                            'constraint': constraint,
                            'old_weight': old_weight,
                            'new_weight': new_weight,
                            'reason': f'持续违反(平均{avg_violations:.1f}次)',
                            'fitness_score': fitness_score
                        }
                        self.adjustment_history.append(adjustment_record)
                        
                        print(f"  调整 {constraint}: {old_weight} → {new_weight}")
                        print(f"  原因: {adjustment_record['reason']}")
                        adjustments_made = True
        
        # 策略3: 如果适应度有改善，可以适当恢复权重
        if fitness_score > 80 and len(self.adjustment_history) > 0:
            # 尝试恢复部分权重
            for constraint, base_weight in self.base_weights.items():
                current_weight = self.current_weights[constraint]
                if current_weight < base_weight * 0.8:  # 如果权重被大幅降低
                    # 逐步恢复
                    new_weight = min(base_weight, current_weight * 1.1)
                    if new_weight != current_weight:
                        self.current_weights[constraint] = new_weight
                        print(f"  恢复 {constraint}: {current_weight} → {new_weight}")
                        adjustments_made = True
        
        if adjustments_made:
            print("  权重调整完成")
        else:
            print("  无需调整权重")
            
        return adjustments_made
    
    def get_dynamic_weights(self):
        """获取当前动态权重"""
        return self.current_weights.copy()
    
    def reset_weights(self):
        """重置为基础权重"""
        self.current_weights = self.base_weights.copy()
        self.violation_history.clear()
        self.adjustment_history.clear()
        print("约束权重已重置为基础配置")
    
    def print_weight_summary(self):
        """打印权重总结"""
        print("\n=== 约束权重状态总结 ===")
        for constraint, weight in self.current_weights.items():
            base_weight = self.base_weights[constraint]
            if weight != base_weight:
                change_pct = (weight - base_weight) / base_weight * 100
                print(f"{constraint}: {weight} (基础: {base_weight}, 变化: {change_pct:+.1f}%)")
            else:
                print(f"{constraint}: {weight}")
        
        if self.adjustment_history:
            print(f"\n总调整次数: {len(self.adjustment_history)}")
            print("最近3次调整:")
            for record in self.adjustment_history[-3:]:
                print(f"  {record['constraint']}: {record['old_weight']} → {record['new_weight']} "
                      f"({record['reason']})")

def integrate_dynamic_constraints_to_main():
    """将动态约束管理集成到主程序的代码片段"""
    integration_code = '''
    # 在ExamScheduler.__init__()中添加:
    self.constraint_manager = DynamicConstraintManager()
    
    # 在calculate_fitness()方法中替换固定权重:
    def calculate_fitness(self, schedule):
        """使用动态权重计算适应度分数"""
        
        # 获取当前动态权重
        weights = self.constraint_manager.get_dynamic_weights()
        
        # 原有计算逻辑，但使用动态权重
        penalty = 0
        
        # 场次限制约束 - 使用动态权重
        for teacher in self.teachers:
            assigned_count = teacher.current_sessions
            if assigned_count != teacher.session_limit:
                penalty += abs(assigned_count - teacher.session_limit) * weights['session_limits']
        
        # 其他约束类似...
        
        return max(0, 100 - penalty / 100)
    
    # 在optimize()方法中添加动态调整:
    def optimize_with_dynamic_constraints(self):
        """带动态约束调整的优化过程"""
        
        for generation in range(self.max_generations):
            # 原有遗传算法逻辑...
            
            # 每10代检查一次约束冲突并调整权重
            if generation % 10 == 0:
                conflicts = self.constraint_manager.detect_constraint_conflicts(
                    self.best_schedule, self)
                current_fitness = self.best_schedule.calculate_fitness(self)
                
                adjusted = self.constraint_manager.adjust_weights_adaptive(
                    conflicts, current_fitness)
                
                if adjusted:
                    # 权重调整后，重新评估当前种群
                    self._reevaluate_population()
    '''
    
    print("=== 动态约束集成代码 ===")
    print(integration_code)

if __name__ == "__main__":
    # 演示动态约束管理
    manager = DynamicConstraintManager()
    
    # 模拟约束冲突
    test_conflicts = {
        'session_limits': 5,
        'must_subjects': 3,
        'room_capacity': 2,
        'forbidden_subjects': 0,
        'time_conflicts': 0,
        'room_preferences': 1,
        'subject_preferences': 0,
        'workload_balance': 0
    }
    
    print("=== 动态约束权重调整演示 ===")
    manager.print_weight_summary()
    
    # 模拟低适应度分数触发权重调整
    manager.adjust_weights_adaptive(test_conflicts, 45.5)
    manager.print_weight_summary()
    
    # 显示集成代码
    integrate_dynamic_constraints_to_main() 