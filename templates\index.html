{% extends "base.html" %}

{% block title %}{{ title }} - 均程通用监考安排{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        border-radius: 12px;
        overflow: hidden;
        position: relative;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%23ffffff" fill-opacity="0.1" d="M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,213.3C672,192,768,128,864,128C960,128,1056,192,1152,208C1248,224,1344,192,1392,176L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
        background-position: center bottom;
        background-repeat: no-repeat;
        background-size: 100%;
        opacity: 0.6;
        z-index: 0;
    }

    .hero-content {
        position: relative;
        z-index: 1;
    }

    .feature-card {
        height: 100%;
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-10px);
    }

    .feature-card .icon-wrapper {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(var(--bs-primary-rgb), 0.1);
        color: var(--primary-color);
        transition: all 0.3s ease;
    }

    .feature-card:hover .icon-wrapper {
        background-color: var(--primary-color);
        color: white;
        transform: scale(1.1);
    }

    .stats-section {
        background-color: white;
        border-radius: 12px;
        box-shadow: var(--card-shadow);
    }

    .stat-item {
        padding: 30px 20px;
        text-align: center;
        border-right: 1px solid rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.05);
    }

    .stat-item:last-child {
        border-right: none;
    }

    .stat-number {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 10px;
        line-height: 1;
    }

    .stat-number i {
        transition: transform 0.3s ease;
    }

    .stat-item:hover .stat-number i {
        transform: scale(1.1);
    }

    .stat-label {
        font-size: 1.1rem;
        font-weight: 500;
        color: var(--text-color);
        margin-bottom: 10px;
    }

    .stat-item .small {
        line-height: 1.5;
    }

    @media (max-width: 768px) {
        .stat-item {
            border-right: none;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .stat-item:last-child {
            border-bottom: none;
        }
    }

    /* Process Section Styles */
    .process-timeline {
        position: relative;
        z-index: 1;
    }

    .process-timeline::before {
        content: '';
        position: absolute;
        top: 50px;
        left: 15%;
        right: 15%;
        height: 2px;
        background: linear-gradient(90deg, 
            var(--primary-color) 0%, 
            var(--primary-color) 33%, 
            var(--primary-light) 100%);
        z-index: -1;
    }

    .process-step {
        text-align: center;
        padding: 0 15px;
    }

    .process-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 20px;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        transition: all 0.3s ease;
    }

    .process-icon i {
        font-size: 2rem;
        z-index: 2;
    }

    .step-number {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 30px;
        height: 30px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9rem;
    }

    .process-step:hover .process-icon {
        background: var(--primary-color);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(var(--bs-primary-rgb), 0.3);
    }

    /* Process Details Accordion Styles */
    .accordion-item {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
    }

    .accordion-button {
        font-weight: 500;
        padding: 1rem 1.5rem;
        border-radius: 8px !important;
    }

    .accordion-button:not(.collapsed) {
        color: var(--primary-color);
        background-color: rgba(var(--bs-primary-rgb), 0.05);
    }

    .accordion-button:focus {
        box-shadow: none;
        border-color: rgba(var(--bs-primary-rgb), 0.1);
    }

    .accordion-body {
        padding: 1.5rem;
        background-color: white;
    }

    .accordion-body ul li {
        position: relative;
        padding-left: 0.5rem;
        margin-bottom: 0.75rem;
        color: var(--text-secondary);
    }

    .accordion-body h6 {
        color: var(--text-color);
        font-weight: 600;
    }

    @media (max-width: 768px) {
        .process-timeline::before {
            display: none;
        }

        .process-step {
            margin-bottom: 30px;
        }

        .process-step:last-child {
            margin-bottom: 0;
        }

        .accordion-body .row > div:first-child {
            margin-bottom: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="hero-section p-5">
            <div class="hero-content row align-items-center">
                <div class="col-lg-7 mb-4 mb-lg-0">
                    <h1 class="display-4 fw-bold mb-3">均程通用监考安排智能解决方案</h1>
                    <p class="lead mb-4">使用先进的算法，自动生成公平、高效的监考安排表，节省时间并提高工作效率。</p>
                    <div class="d-flex flex-wrap gap-2">
                        {% if current_user.is_authenticated %}
                        <a href="{{ url_for('dashboard') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>进入控制台
                        </a>
                        <a href="{{ url_for('new_task') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-plus-circle me-2"></i>新建任务
                        </a>
                        {% else %}
                        <a href="{{ url_for('login') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>登录系统
                        </a>
                        <a href="{{ url_for('register') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-user-plus me-2"></i>注册账号
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="col-lg-5 text-center">
                    <img src="{{ url_for('static', filename='images/2942789.png') }}" alt="监考安排插图" class="img-fluid" style="max-height: 300px; filter: drop-shadow(0 10px 10px rgba(0,0,0,0.2));">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="stats-section py-4">
            <div class="row g-0">
                <div class="col-md-4 stat-item">
                    <p class="stat-number"><i class="fas fa-bolt"></i></p>
                    <p class="stat-label">高效智能</p>
                    <p class="small text-muted px-3">快速生成监考安排方案，节省大量人工时间</p>
                </div>
                <div class="col-md-4 stat-item">
                    <p class="stat-number"><i class="fas fa-shield-alt"></i></p>
                    <p class="stat-label">安全可靠</p>
                    <p class="small text-muted px-3">数据本地存储，确保信息安全，支持离线运行</p>
                </div>
                <div class="col-md-4 stat-item">
                    <p class="stat-number"><i class="fas fa-check-circle"></i></p>
                    <p class="stat-label">简单易用</p>
                    <p class="small text-muted px-3">操作界面直观友好，一键导入导出数据</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Process Section -->
<div class="row mb-5">
    <div class="col-12 text-center mb-4">
        <h2 class="fw-bold">使用流程</h2>
        <p class="text-muted">规范化的操作流程，确保监考安排准确无误</p>
    </div>

    <div class="col-12">
        <div class="card border-0">
            <div class="card-body p-4">
                <div class="row process-timeline">
                    <div class="col-md-3 process-step">
                        <div class="process-icon">
                            <span class="step-number">1</span>
                            <i class="fas fa-file-download"></i>
                        </div>
                        <h5 class="mt-3">下载模板</h5>
                        <p class="text-muted small">获取标准Excel模板，查看填写说明</p>
                    </div>
                    <div class="col-md-3 process-step">
                        <div class="process-icon">
                            <span class="step-number">2</span>
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h5 class="mt-3">数据验证</h5>
                        <p class="text-muted small">上传文件进行格式和数据验证</p>
                    </div>
                    <div class="col-md-3 process-step">
                        <div class="process-icon">
                            <span class="step-number">3</span>
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h5 class="mt-3">创建任务</h5>
                        <p class="text-muted small">验证通过后创建监考安排任务</p>
                    </div>
                    <div class="col-md-3 process-step">
                        <div class="process-icon">
                            <span class="step-number">4</span>
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h5 class="mt-3">生成安排</h5>
                        <p class="text-muted small">一键生成并导出监考安排方案</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Process Details -->
    <div class="col-12 mt-4">
        <div class="accordion" id="processAccordion">
            <div class="accordion-item border-0 mb-3">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed bg-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                        <i class="fas fa-info-circle me-2"></i>查看详细操作步骤
                    </button>
                </h2>
                <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#processAccordion">
                    <div class="accordion-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="mb-3"><i class="fas fa-file-download text-primary me-2"></i>第一步：下载和填写模板</h6>
                                <ul class="list-unstyled ps-4 mb-4">
                                    <li class="mb-2">• 从模板说明页面下载标准Excel模板</li>
                                    <li class="mb-2">• 按照说明填写监考员、考试科目、考场信息</li>
                                    <li class="mb-2">• 确保填写格式符合要求</li>
                                </ul>

                                <h6 class="mb-3"><i class="fas fa-check-circle text-primary me-2"></i>第二步：数据验证</h6>
                                <ul class="list-unstyled ps-4 mb-4">
                                    <li class="mb-2">• 上传填写好的Excel文件</li>
                                    <li class="mb-2">• 系统自动检查数据格式和完整性</li>
                                    <li class="mb-2">• 查看验证结果和错误提示（如有）</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="mb-3"><i class="fas fa-tasks text-primary me-2"></i>第三步：创建任务</h6>
                                <ul class="list-unstyled ps-4 mb-4">
                                    <li class="mb-2">• 验证通过后创建新任务</li>
                                    <li class="mb-2">• 输入任务名称和描述</li>
                                    <li class="mb-2">• 确认任务信息无误</li>
                                </ul>

                                <h6 class="mb-3"><i class="fas fa-calendar-check text-primary me-2"></i>第四步：生成安排</h6>
                                <ul class="list-unstyled ps-4">
                                    <li class="mb-2">• 点击开始处理按钮</li>
                                    <li class="mb-2">• 实时查看处理进度</li>
                                    <li class="mb-2">• 下载生成的监考安排表</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="row mb-5">
    <div class="col-12 text-center mb-4">
        <h2 class="fw-bold">系统功能</h2>
        <p class="text-muted">提供全方位的监考安排解决方案</p>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card feature-card h-100 border-0">
            <div class="card-body text-center p-4">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-calendar-alt fa-2x"></i>
                </div>
                <h4 class="card-title">智能排考</h4>
                <p class="card-text">根据教师可用时间和监考要求，使用先进算法自动生成最优监考安排方案。</p>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card feature-card h-100 border-0">
            <div class="card-body text-center p-4">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-balance-scale fa-2x"></i>
                </div>
                <h4 class="card-title">公平分配</h4>
                <p class="card-text">系统自动平衡教师工作量，确保监考任务在教师间公平分配，避免工作量不均。</p>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card feature-card h-100 border-0">
            <div class="card-body text-center p-4">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-file-excel fa-2x"></i>
                </div>
                <h4 class="card-title">结果导出</h4>
                <p class="card-text">一键导出格式化的Excel监考安排表，包含详细统计信息，方便查看、分发和打印。</p>
            </div>
        </div>
    </div>
</div>

<!-- Additional Features -->
<div class="row mb-5">
    <div class="col-md-6 mb-4">
        <div class="card h-100 border-0">
            <div class="card-body p-4">
                <h4><i class="fas fa-cogs text-primary me-2"></i> 高级功能</h4>
                <ul class="list-unstyled mt-3">
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> 自定义约束条件设置</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> 教师特殊时间要求处理</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> 多维度统计分析</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> 历史数据对比分析</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100 border-0">
            <div class="card-body p-4">
                <h4><i class="fas fa-shield-alt text-primary me-2"></i> 系统优势</h4>
                <ul class="list-unstyled mt-3">
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> 高效的算法引擎</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> 直观的用户界面</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> 实时处理进度反馈</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> 完善的数据导入导出</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="cta-section p-4 p-md-5">
            <div class="row align-items-center">
                <div class="col-md-8 mb-3 mb-md-0">
                    <h3 class="mb-2">准备好开始使用了吗？</h3>
                    <p class="mb-0 text-muted">立即注册账号，体验均程通用监考安排。</p>
                </div>
                <div class="col-md-4 text-md-end">
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('new_task') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus-circle me-2"></i>创建新任务
                    </a>
                    {% else %}
                    <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>立即注册
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
