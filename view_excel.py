#!/usr/bin/env python3
import pandas as pd

# 读取Excel文件
print("=== 监考员设置 ===")
teacher_df = pd.read_excel('监考安排.xlsx', sheet_name='监考员设置')
print(teacher_df)

print("\n=== 考试科目设置 ===")
subject_df = pd.read_excel('监考安排.xlsx', sheet_name='考试科目设置')
print(subject_df)

print("\n=== 考场设置 ===")
room_df = pd.read_excel('监考安排.xlsx', sheet_name='考场设置')
print(room_df)

# 专门查看李安华的约束条件
print("\n=== 李安华的具体约束条件 ===")
lihua_row = teacher_df[teacher_df['监考老师'] == '李安华']
if not lihua_row.empty:
    print("李安华的约束条件:")
    for col in teacher_df.columns:
        value = lihua_row[col].iloc[0]
        print(f"  {col}: {value}")
else:
    print("未找到李安华的记录") 