{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- 左侧导航 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-column align-items-center text-center mb-4">
                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center"
                             style="width: 80px; height: 80px; font-size: 2rem;">
                            {{ current_user.username[0].upper() }}
                        </div>
                        <h5 class="mt-3 mb-1">{{ current_user.username }}</h5>
                        <span class="text-muted">{{ current_user.role|title }}</span>
                    </div>
                    <div class="nav flex-column nav-pills" role="tablist">
                        <button class="nav-link active mb-2" data-bs-toggle="pill" data-bs-target="#basic-info">
                            <i class="fas fa-user me-2"></i>基本信息
                        </button>
                        <button class="nav-link mb-2" data-bs-toggle="pill" data-bs-target="#security">
                            <i class="fas fa-shield-alt me-2"></i>安全设置
                        </button>
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#login-history">
                            <i class="fas fa-history me-2"></i>登录历史
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="col-md-9">
            <div class="tab-content">
                <!-- 基本信息 -->
                <div class="tab-pane fade show active" id="basic-info">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">基本信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <label class="col-sm-3 col-form-label">用户名</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext">{{ current_user.username }}</p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label class="col-sm-3 col-form-label">邮箱地址</label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <input type="email" class="form-control" id="email" value="{{ current_user.email }}">
                                        <button class="btn btn-primary" onclick="updateEmail()">更新</button>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label class="col-sm-3 col-form-label">账户类型</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext">
                                        {% if current_user.role == 'admin' %}
                                        管理员
                                        {% elif current_user.role == 'vip' %}
                                        VIP用户
                                        {% else %}
                                        普通用户
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label class="col-sm-3 col-form-label">账号创建时间</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext">{{ current_user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label class="col-sm-3 col-form-label">账号有效期</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext">
                                        {% if current_user.expiry_date %}
                                        {{ current_user.expiry_date.strftime('%Y-%m-%d') }}
                                        {% if current_user.expiry_date < now %}
                                        <span class="badge bg-danger ms-2">已过期</span>
                                        {% elif (current_user.expiry_date - now).days <= 7 %}
                                        <span class="badge bg-warning ms-2">即将到期</span>
                                        {% endif %}
                                        {% else %}
                                        永久有效
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            <div class="row">
                                <label class="col-sm-3 col-form-label">剩余任务配额</label>
                                <div class="col-sm-9">
                                    <p class="form-control-plaintext">
                                        {% if current_user.role == 'admin' %}
                                        无限制
                                        {% else %}
                                        {{ current_user.task_limit - current_user.tasks.count() }}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全设置 -->
                <div class="tab-pane fade" id="security">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">修改密码</h5>
                        </div>
                        <div class="card-body">
                            <form id="changePasswordForm" onsubmit="return changePassword(event)">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <div class="mb-3">
                                    <label class="form-label">原密码</label>
                                    <input type="password" class="form-control" id="oldPassword" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">新密码</label>
                                    <input type="password" class="form-control" id="newPassword" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">确认新密码</label>
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary">修改密码</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 登录历史 -->
                <div class="tab-pane fade" id="login-history">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">最近登录记录</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                上次登录时间：{{ current_user.last_login.strftime('%Y-%m-%d %H:%M:%S') if current_user.last_login else '首次登录' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认修改邮箱对话框 -->
<div class="modal fade" id="confirmEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认修改邮箱</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要修改邮箱地址吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmUpdateEmail()">确认修改</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 修改密码
async function changePassword(event) {
    event.preventDefault();

    const oldPassword = document.getElementById('oldPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // 验证新密码
    if (newPassword !== confirmPassword) {
        alert('两次输入的新密码不一致');
        return;
    }

    try {
        const response = await fetch('/profile/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            },
            body: JSON.stringify({
                old_password: oldPassword,
                new_password: newPassword
            })
        });

        const data = await response.json();
        if (data.success) {
            alert('密码修改成功');
            document.getElementById('changePasswordForm').reset();
        } else {
            alert(data.message || '修改失败，请稍后重试');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('修改失败，请稍后重试');
    }
}

// 更新邮箱
let emailModal;
function updateEmail() {
    emailModal = new bootstrap.Modal(document.getElementById('confirmEmailModal'));
    emailModal.show();
}

async function confirmUpdateEmail() {
    const newEmail = document.getElementById('email').value;

    try {
        const response = await fetch('/profile/update-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            },
            body: JSON.stringify({
                email: newEmail
            })
        });

        const data = await response.json();
        if (data.success) {
            alert('邮箱更新成功');
            emailModal.hide();
        } else {
            alert(data.message || '更新失败，请稍后重试');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('更新失败，请稍后重试');
    }
}
</script>
{% endblock %}