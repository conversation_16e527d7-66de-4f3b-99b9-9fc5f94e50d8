#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
环境设置脚本
用于安装和验证所有必要的依赖
"""

import os
import sys
import subprocess
import platform
import importlib.util
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('setup.log')
    ]
)
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本是否满足要求"""
    required_version = (3, 8)
    current_version = sys.version_info
    
    if current_version < required_version:
        logger.error(f"Python版本不满足要求: 当前版本 {'.'.join(map(str, current_version[:3]))}, 需要版本 {'.'.join(map(str, required_version))}")
        return False
    
    logger.info(f"Python版本检查通过: {'.'.join(map(str, current_version[:3]))}")
    return True

def check_pip():
    """检查pip是否可用"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], check=True, capture_output=True)
        logger.info("pip检查通过")
        return True
    except subprocess.CalledProcessError:
        logger.error("pip不可用，请安装pip")
        return False

def install_dependencies():
    """安装依赖"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        logger.error(f"找不到依赖文件: {requirements_file}")
        return False
    
    try:
        logger.info("开始安装依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", requirements_file], check=True)
        logger.info("依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"安装依赖时出错: {e}")
        return False

def check_dependencies():
    """检查关键依赖是否已安装"""
    critical_packages = [
        "flask", "sqlalchemy", "pandas", "openpyxl", "ortools", "redis"
    ]
    
    missing_packages = []
    
    for package in critical_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少关键依赖: {', '.join(missing_packages)}")
        return False
    
    logger.info("关键依赖检查通过")
    return True

def check_optional_dependencies():
    """检查可选依赖是否已安装"""
    optional_packages = [
        "psutil", "fakeredis", "structlog", "flask_talisman", "flask_cors"
    ]
    
    missing_packages = []
    
    for package in optional_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)
    
    if missing_packages:
        logger.warning(f"缺少可选依赖: {', '.join(missing_packages)}")
    else:
        logger.info("可选依赖检查通过")
    
    return len(missing_packages) == 0

def check_environment():
    """检查环境配置"""
    # 检查操作系统
    os_name = platform.system()
    logger.info(f"操作系统: {os_name} {platform.version()}")
    
    # 检查Python环境
    logger.info(f"Python路径: {sys.executable}")
    logger.info(f"Python版本: {platform.python_version()}")
    
    # 检查是否在虚拟环境中
    in_venv = sys.prefix != sys.base_prefix
    logger.info(f"是否在虚拟环境中: {'是' if in_venv else '否'}")
    
    # 检查临时目录
    temp_dir = os.path.join(os.getcwd(), "uploads", "tmp")
    if not os.path.exists(temp_dir):
        try:
            os.makedirs(temp_dir, exist_ok=True)
            logger.info(f"创建临时目录: {temp_dir}")
        except Exception as e:
            logger.error(f"创建临时目录失败: {e}")
    else:
        logger.info(f"临时目录已存在: {temp_dir}")
    
    # 检查日志目录
    log_dir = os.path.join(os.getcwd(), "logs")
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir, exist_ok=True)
            logger.info(f"创建日志目录: {log_dir}")
        except Exception as e:
            logger.error(f"创建日志目录失败: {e}")
    else:
        logger.info(f"日志目录已存在: {log_dir}")
    
    return True

def main():
    """主函数"""
    logger.info("开始环境设置...")
    
    # 检查Python版本
    if not check_python_version():
        logger.error("环境设置失败: Python版本不满足要求")
        return False
    
    # 检查pip
    if not check_pip():
        logger.error("环境设置失败: pip不可用")
        return False
    
    # 检查环境
    check_environment()
    
    # 安装依赖
    if not install_dependencies():
        logger.error("环境设置失败: 安装依赖失败")
        return False
    
    # 检查关键依赖
    if not check_dependencies():
        logger.error("环境设置失败: 缺少关键依赖")
        return False
    
    # 检查可选依赖
    check_optional_dependencies()
    
    logger.info("环境设置完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
