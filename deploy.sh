#!/bin/bash
# 监考安排系统一键部署脚本
# 作者：AI助手
# 日期：2024年4月20日

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Python版本
check_python_version() {
    log_step "检查Python版本..."
    python_version=$(python -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")')
    log_info "检测到Python版本: $python_version"

    if [[ $(echo "$python_version < 3.8" | bc) -eq 1 ]]; then
        log_error "Python版本必须 >= 3.8，当前版本为 $python_version"
        exit 1
    fi
}

# 创建配置文件
create_config() {
    log_step "创建生产环境配置文件..."

    if [ -f "config.py" ]; then
        log_warn "配置文件已存在，跳过创建"
        return
    fi

    # 生成随机密钥
    SECRET_KEY=$(python -c 'import secrets; print(secrets.token_hex(32))')

    cat > config.py << EOF
# 监考安排系统生产环境配置
# 自动生成于 $(date)

import os

# 基本配置
SECRET_KEY = '$SECRET_KEY'
DEBUG = False
TESTING = False

# 数据库配置
SQLALCHEMY_DATABASE_URI = 'sqlite:///app.db'
SQLALCHEMY_TRACK_MODIFICATIONS = False

# 上传文件配置
UPLOAD_FOLDER = 'uploads'
MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {'xlsx'}

# 临时文件目录
TMP_DIR = '/tmp/jiankao'

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FILE = 'app.log'

# 安全配置
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
REMEMBER_COOKIE_SECURE = True
REMEMBER_COOKIE_HTTPONLY = True

# 缓存目录配置
CACHE_DIR = os.path.join(TMP_DIR, 'cache')

# 任务处理配置
DEFAULT_TIME_LIMIT = 300  # 默认求解时间限制(秒)
MAX_DURATION_DIFF = 1.0  # 同组教师间允许的最大监考时长差异(小时)
EOF

    log_info "配置文件已创建: config.py"
}

# 创建虚拟环境
create_virtual_env() {
    log_step "创建虚拟环境..."

    if [ -d "venv" ]; then
        log_warn "虚拟环境已存在，跳过创建"
        return
    fi

    python -m venv venv
    log_info "虚拟环境已创建"
}

# 安装依赖
install_dependencies() {
    log_step "安装依赖..."

    source venv/bin/activate

    # 升级pip
    pip install --upgrade pip

    # 安装生产环境依赖
    pip install -r requirements.txt

    # 安装额外的生产环境依赖
    pip install gunicorn supervisor

    log_info "依赖安装完成"
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."

    source venv/bin/activate

    # 创建数据库目录
    mkdir -p instance

    # 运行数据库初始化脚本
    python -c "
from app import app, db
from app import User
with app.app_context():
    db.create_all()
    # 检查管理员账号是否存在
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            task_limit=999
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print('管理员账号已创建')
    else:
        print('管理员账号已存在')
"

    log_info "数据库初始化完成"
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."

    # 创建上传目录
    mkdir -p uploads

    # 创建日志目录
    mkdir -p logs

    # 创建模板目录
    mkdir -p template-guide

    # 创建临时目录
    sudo mkdir -p /tmp/jiankao/cache
    sudo mkdir -p /tmp/jiankao/tmp

    # 设置权限
    chmod -R 755 uploads
    chmod -R 755 logs
    sudo chmod -R 777 /tmp/jiankao

    log_info "目录结构创建完成"
}

# 创建启动脚本
create_startup_script() {
    log_step "创建启动脚本..."

    cat > start.sh << EOF
#!/bin/bash
# 监考安排系统启动脚本
# 自动生成于 $(date)

# 确保临时目录存在
mkdir -p /tmp/jiankao/cache
mkdir -p /tmp/jiankao/tmp
chmod -R 777 /tmp/jiankao

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export FLASK_APP=app.py
export FLASK_ENV=production

# 启动Gunicorn服务器
gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 120 --log-file=logs/gunicorn.log app:app
EOF

    chmod +x start.sh
    log_info "启动脚本已创建: start.sh"
}

# 创建停止脚本
create_stop_script() {
    log_step "创建停止脚本..."

    cat > stop.sh << EOF
#!/bin/bash
# 监考安排系统停止脚本
# 自动生成于 $(date)

# 查找并终止Gunicorn进程
pkill -f "gunicorn" || echo "没有找到运行中的Gunicorn进程"

# 清理临时文件
echo "清理临时文件..."
find /tmp/jiankao -type f -mtime +7 -delete
EOF

    chmod +x stop.sh
    log_info "停止脚本已创建: stop.sh"
}

# 创建Windows启动脚本
create_windows_scripts() {
    log_step "创建Windows启动脚本..."

    # 创建启动脚本
    cat > start.bat << EOF
@echo off
REM 监考安排系统Windows启动脚本
REM 自动生成于 $(date)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 设置环境变量
set FLASK_APP=app.py
set FLASK_ENV=production

REM 启动服务器
python -m waitress --host=0.0.0.0 --port=5000 app:app

pause
EOF

    # 安装waitress (Windows下的WSGI服务器)
    source venv/bin/activate || source venv/Scripts/activate
    pip install waitress

    log_info "Windows启动脚本已创建: start.bat"
}

# 主函数
main() {
    log_info "开始部署监考安排系统..."

    # 检查操作系统
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        IS_WINDOWS=true
        log_info "检测到Windows操作系统"
    else
        IS_WINDOWS=false
        log_info "检测到类Unix操作系统"
    fi

    # 执行部署步骤
    check_python_version
    create_config
    create_virtual_env
    install_dependencies
    init_database
    create_directories

    # 根据操作系统创建不同的启动脚本
    if [ "$IS_WINDOWS" = true ]; then
        create_windows_scripts
    else
        create_startup_script
        create_stop_script
    fi

    log_info "部署完成！"

    # 显示使用说明
    if [ "$IS_WINDOWS" = true ]; then
        echo -e "${GREEN}使用说明:${NC}"
        echo -e "1. 双击 start.bat 启动应用"
        echo -e "2. 访问 http://localhost:5000 使用系统"
        echo -e "3. 默认管理员账号: admin / admin123"
    else
        echo -e "${GREEN}使用说明:${NC}"
        echo -e "1. 执行 ./start.sh 启动应用"
        echo -e "2. 执行 ./stop.sh 停止应用"
        echo -e "3. 访问 http://localhost:5000 使用系统"
        echo -e "4. 默认管理员账号: admin / admin123"
    fi
}

# 执行主函数
main
