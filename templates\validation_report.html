{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>验证报告
                    </h2>
                    <div>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>返回
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {{ report_content | safe }}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-report {
    font-size: 14px;
}

.error-report h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.error-report h4 {
    color: #34495e;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.summary-section .card {
    background-color: #f8f9fa;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.error-details-section table {
    font-size: 13px;
}

.error-details-section th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.error-details-section td {
    vertical-align: middle;
}

.info-section .list-group-item {
    font-size: 13px;
    border-left: 3px solid #3498db;
}

.badge {
    font-size: 0.9em;
    padding: 0.4em 0.8em;
}

.bg-success {
    background-color: #2ecc71 !important;
}

.bg-danger {
    background-color: #e74c3c !important;
}
</style>
{% endblock %} 