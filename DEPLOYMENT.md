# 监考安排系统部署指南

本文档提供了监考安排系统的部署步骤和说明。

## 系统要求

- Python 3.8 或更高版本
- 足够的磁盘空间（建议至少 1GB）
- 内存建议至少 4GB

## 部署步骤

### Windows 环境

1. **一键部署**

   双击运行 `deploy.bat` 脚本，该脚本将自动执行以下操作：
   
   - 检查 Python 版本
   - 创建生产环境配置文件
   - 创建虚拟环境
   - 安装依赖
   - 初始化数据库
   - 创建必要的目录结构
   - 创建启动脚本

2. **启动应用**

   部署完成后，双击 `start.bat` 启动应用。

### Linux/Mac 环境

1. **一键部署**

   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

   该脚本将自动执行以下操作：
   
   - 检查 Python 版本
   - 创建生产环境配置文件
   - 创建虚拟环境
   - 安装依赖
   - 初始化数据库
   - 创建必要的目录结构
   - 创建启动和停止脚本

2. **启动应用**

   ```bash
   ./start.sh
   ```

3. **停止应用**

   ```bash
   ./stop.sh
   ```

## 配置说明

部署脚本会创建一个 `config.py` 文件，其中包含生产环境的配置参数。您可以根据需要修改以下配置：

- `SECRET_KEY`: 应用密钥，用于会话安全
- `SQLALCHEMY_DATABASE_URI`: 数据库连接字符串
- `UPLOAD_FOLDER`: 上传文件存储目录
- `MAX_CONTENT_LENGTH`: 上传文件大小限制
- `LOG_LEVEL`: 日志级别
- `LOG_FILE`: 日志文件路径

## 访问应用

部署完成后，可以通过以下URL访问应用：

```
http://localhost:5000
```

默认管理员账号：
- 用户名: admin
- 密码: admin123

**重要提示：** 首次登录后，请立即修改管理员密码！

## 数据备份

建议定期备份以下文件和目录：

- `app.db`: 数据库文件
- `uploads/`: 上传的文件
- `instance/`: 实例配置目录

## 故障排除

1. **应用无法启动**

   检查日志文件 `app.log` 和 `logs/` 目录中的日志文件，查找错误信息。

2. **依赖安装失败**

   尝试手动安装依赖：
   
   ```bash
   # Windows
   venv\Scripts\activate
   pip install -r requirements.txt
   
   # Linux/Mac
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. **数据库错误**

   如果数据库损坏，可以尝试重新初始化：
   
   ```bash
   # 激活虚拟环境
   source venv/bin/activate  # Linux/Mac
   venv\Scripts\activate     # Windows
   
   # 删除现有数据库
   rm app.db
   
   # 重新初始化
   python -c "from app import app, db; with app.app_context(): db.create_all()"
   ```

## 安全建议

1. 在生产环境中，建议使用反向代理（如 Nginx）来提供服务
2. 启用 HTTPS 以保护数据传输
3. 定期更新系统和依赖包
4. 定期备份数据
5. 修改默认管理员密码
6. 限制服务器访问权限

## 更新应用

更新应用时，请按照以下步骤操作：

1. 备份数据库和上传文件
2. 更新代码
3. 重新运行部署脚本
4. 重启应用
