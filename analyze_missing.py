#!/usr/bin/env python3
import pandas as pd

# 读取最新的结果文件
result_file = '监考安排_20250602_155018.xlsx'

print("=== 详细分析所有缺1的情况 ===")
room_result = pd.read_excel(result_file, sheet_name='考场安排')

# 查找所有缺1的情况
missing_cases = []
for index, row in room_result.iterrows():
    room_name = row['考场']
    for subject in ['语文', '数学', '英语', '理化', '政史']:
        teachers = str(row[subject])
        if '缺1' in teachers:
            missing_cases.append({
                '考场': room_name,
                '科目': subject,
                '详情': teachers
            })

if missing_cases:
    print("所有缺1的情况:")
    for case in missing_cases:
        print(f"  {case['考场']}-{case['科目']}: {case['详情']}")
else:
    print("没有发现任何缺1的情况")

print("\n=== 检查未安排场次表 ===")
try:
    unassigned = pd.read_excel(result_file, sheet_name='未安排场次')
    if unassigned.empty:
        print("未安排场次表为空")
    else:
        print("未安排场次表内容:")
        print(unassigned.to_string(index=False))
except Exception as e:
    print(f"读取未安排场次表时出错: {e}")

print("\n=== 检查所有教师的安排情况 ===")
teacher_result = pd.read_excel(result_file, sheet_name='监考员安排')

# 查找有未安排场次的教师
unassigned_teachers = teacher_result[teacher_result['未安排场次'] > 0]
if not unassigned_teachers.empty:
    print("有未安排场次的教师:")
    for index, row in unassigned_teachers.iterrows():
        print(f"  {row['监考老师']}: 限制{row['场次限制']}场, 实际{row['实际安排次数']}场, 未安排{row['未安排场次']}场")
else:
    print("没有教师有未安排场次")

print("\n=== 李安华具体分析 ===")
lihua_result = teacher_result[teacher_result['监考老师'] == '李安华']
if not lihua_result.empty:
    lihua_data = lihua_result.iloc[0]
    print(f"李安华:")
    print(f"  场次限制: {lihua_data['场次限制']}")
    print(f"  实际安排次数: {lihua_data['实际安排次数']}")
    print(f"  未安排场次: {lihua_data['未安排场次']}")
    
    # 检查具体安排
    subjects = ['语文', '数学', '英语', '理化', '政史']
    arranged_subjects = []
    for subject in subjects:
        value = lihua_data[subject]
        if pd.notna(value) and str(value).strip() != '' and str(value) != '0':
            arranged_subjects.append(f"{subject}({value})")
    
    print(f"  具体安排: {', '.join(arranged_subjects) if arranged_subjects else '无'}")
    
    if lihua_data['场次限制'] == lihua_data['实际安排次数']:
        print("  结论: 李安华已经完全安排，没有缺少场次")
    else:
        print(f"  结论: 李安华确实缺少 {lihua_data['未安排场次']} 场") 