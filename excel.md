# 监考安排Excel模板说明

## 文件结构

Excel文件包含三个必需的工作表（Sheet）：监考员设置、考试科目设置、考场设置。

### 1. 监考员设置表

**Sheet名称**：`监考员设置`

**列名及说明**：
- `序号`：从1开始的连续数字，作为教师ID
- `监考老师`：教师姓名
- `任教科目`：教师所教授的科目
- `必监考科目`：教师必须监考的科目列表（支持多种分隔符）
- `不监考科目`：教师不能监考的科目列表
- `必监考考场`：教师必须监考的考场列表
- `不监考考场`：教师不能监考的考场列表
- `场次限制`：教师可以监考的最大场次数（必须为正整数）

### 2. 考试科目设置表

**Sheet名称**：`考试科目设置`

**列名及说明**：
- `课程代码`：科目的唯一标识
- `课程名称`：科目名称
- `开始时间`：考试开始时间
- `结束时间`：考试结束时间

### 3. 考场设置表

**Sheet名称**：`考场设置`

**列结构**：
- 第一列为`考场`：考场名称
- 其余列为科目名称：每个单元格填写该考场该科目需要的监考教师数量

## 数据规范

### 分隔符支持
在需要填写多个值的字段（如必监考科目、不监考科目等），支持以下分隔符：
- 中文逗号（，）
- 英文逗号（,）
- 顿号（、）
- 分号（；）
- 斜杠（／）
- 空格

### 特殊值处理
- 空值处理：空白单元格视为无限制
- 数字0：等同于空值
- 场次限制：必须为大于0的整数

## 验证规则

### 1. 基本验证
- 所有必填字段不能为空
- 科目名称必须在考试科目设置中存在
- 考场名称必须在考场设置中存在
- 时间格式必须正确
- 教师场次限制总和必须大于等于总监考需求

### 2. 逻辑验证
- 必监考科目和不监考科目不能有重复项
- 必监考考场和不监考考场不能有重复项
- 同一教师不能被安排在时间冲突的考试中
- 教师的场次限制必须满足其必监考科目的要求

### 3. 关联验证
- 考场设置表中的科目必须在考试科目设置表中存在
- 监考员设置表中引用的科目必须在考试科目设置表中存在
- 监考员设置表中引用的考场必须在考场设置表中存在

## 使用注意事项

1. **数据一致性**
   - 保持科目名称在各表间的一致性
   - 考场名称必须保持一致
   - 时间格式建议使用标准格式（如：2024-03-15 09:00）

2. **限制设置**
   - 合理设置场次限制，确保总体监考力量满足需求
   - 避免过多的必监考/禁止监考限制，以免造成无法安排的情况
   - 注意检查时间冲突

3. **数据填写**
   - 使用统一的分隔符，建议使用中文逗号
   - 避免在文本中输入多余的空格
   - 确保数字字段不含有文本或特殊字符

4. **常见问题**
   - 场次限制总和不足：检查每位教师的场次限制是否合理
   - 时间冲突：检查考试时间安排是否合理
   - 无法安排：检查限制条件是否过于严格

## 模板逻辑示例

```python
# 基本约束检查
for teacher in teachers:
    assert teacher.max_sessions > 0  # 场次限制必须为正整数
    assert not (teacher.must_subjects & teacher.forbidden_subjects)  # 检查科目冲突

# 时间冲突检查
for teacher in teachers:
    for exam1, exam2 in combinations(exams, 2):
        if has_time_overlap(exam1, exam2):
            assert not (teacher in exam1.invigilators and teacher in exam2.invigilators)

# 监考员总量检查
total_required = sum(room.subject_requirements.values())
total_capacity = sum(teacher.max_sessions for teacher in teachers)
assert total_capacity >= total_required
``` 