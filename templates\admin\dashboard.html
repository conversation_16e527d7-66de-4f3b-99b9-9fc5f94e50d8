{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <h1 class="h2 mb-4">仪表盘</h1>

    <div class="row">
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <h5 class="card-title">总用户数</h5>
                    <p class="card-text display-4">{{ total_users }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <h5 class="card-title">总任务数</h5>
                    <p class="card-text display-4">{{ total_tasks }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <h5 class="card-title">进行中的任务</h5>
                    <p class="card-text display-4">{{ active_tasks }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <h5 class="card-title">已完成的任务</h5>
                    <p class="card-text display-4">{{ completed_tasks }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    系统状态
                </div>
                <div class="card-body">
                    <p>系统运行正常</p>
                    <p>当前时间：{{ now.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                    <p>系统版本：1.0.0</p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    快速操作
                </div>
                <div class="card-body">
                    <a href="{{ url_for('admin_users') }}" class="btn btn-primary mb-2">管理用户</a>
                    <a href="{{ url_for('admin_tasks') }}" class="btn btn-success mb-2">管理任务</a>
                    <a href="{{ url_for('admin_settings') }}" class="btn btn-info mb-2">系统设置</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 