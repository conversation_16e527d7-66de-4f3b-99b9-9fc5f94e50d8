#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import pandas as pd
import numpy as np
import uuid
import logging
import re
from datetime import datetime
from typing import Dict, List, Callable, Optional, Set, Tuple, Any

# 配置日志
logger = logging.getLogger('jiankao_app.validator')

class BaseExcelValidator:
    """基础Excel验证器：提供基本的验证框架和通用功能"""

    def __init__(self, file_path: str, progress_callback: Optional[Callable[[int, str], None]] = None):
        """
        初始化验证器

        参数:
            file_path: Excel文件路径
            progress_callback: 进度回调函数，接收进度百分比和消息
        """
        self.file_path = file_path
        self.progress_callback = progress_callback
        self.workbook = None
        self.errors = []
        self.warnings = []
        self.validation_messages = {
            'errors': [],
            'warnings': [],
            'info': [],
            'examples': {},  # 添加示例数据字典
            'error_report': None  # 错误报告路径
        }
        self.is_valid = True
        self.error_details = []  # 存储详细的错误信息

    def close(self):
        """关闭并释放资源"""
        try:
            if hasattr(self, 'workbook') and self.workbook:
                self.workbook.close()
                self.workbook = None
        except Exception as e:
            logger.error(f"关闭Excel文件时发生错误: {str(e)}")
            
    def __del__(self):
        """析构函数，确保资源被释放"""
        self.close()

    def _update_progress(self, progress: int, message: str):
        """更新验证进度"""
        if self.progress_callback:
            try:
                self.progress_callback(progress, message)
            except Exception as e:
                logger.error(f"更新进度时发生错误: {str(e)}")

    def add_error(self, error: str):
        """添加错误信息"""
        self.errors.append(error)
        self.validation_messages['errors'].append(error)

    def add_warning(self, warning: str):
        """添加警告信息"""
        self.warnings.append(warning)
        self.validation_messages['warnings'].append(warning)

    def add_error_detail(self, sheet: str, row: int, column: str, value: str, error_type: str, message: str):
        """添加详细的错误信息"""
        self.error_details.append({
            'sheet': sheet,
            'row': row,
            'column': column,
            'value': value,
            'error_type': error_type,
            'message': message
        })

    def get_validation_messages(self) -> Dict[str, Any]:
        """获取验证消息"""
        return self.validation_messages

    def _generate_error_report(self) -> Optional[str]:
        """生成错误报告"""
        if not self.errors and not self.warnings:
            return None

        try:
            # 生成报告文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_name = os.path.splitext(os.path.basename(self.file_path))[0]
            report_file = os.path.join(
                os.path.dirname(self.file_path),
                f"validation_report_{timestamp}_{base_name}.xlsx"
            )

            # 创建错误报告
            with pd.ExcelWriter(report_file, engine='openpyxl') as writer:
                # 验证摘要
                summary_data = {
                    '验证项': ['验证状态', '错误数量', '警告数量'],
                    '结果': [
                        '通过' if self.is_valid else '未通过',
                        len(self.errors),
                        len(self.warnings)
                    ]
                }
                pd.DataFrame(summary_data).to_excel(writer, sheet_name='验证摘要', index=False)

                # 错误详情
                if self.error_details:
                    error_df = pd.DataFrame(self.error_details)
                    error_df.to_excel(writer, sheet_name='错误详情', index=False)

                # 警告信息
                if self.warnings:
                    warning_data = {'警告信息': self.warnings}
                    pd.DataFrame(warning_data).to_excel(writer, sheet_name='警告信息', index=False)

            self.validation_messages['error_report'] = report_file
            return report_file

        except Exception as e:
            logger.error(f"生成错误报告时发生错误: {str(e)}")
            return None

class ExcelValidator(BaseExcelValidator):
    """业务Excel验证器：实现具体的业务验证逻辑"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.strict_validation_failed = False

    def validate(self) -> bool:
        """执行所有验证步骤"""
        try:
            # 更新进度：开始验证
            self._update_progress(0, "开始验证...")

            # 打开Excel文件
            self.workbook = pd.ExcelFile(self.file_path)
            self._update_progress(10, "文件已打开...")

            # 检查必要的工作表
            required_sheets = ['监考员设置', '考试科目设置', '考场设置']
            missing_sheets = [sheet for sheet in required_sheets if sheet not in self.workbook.sheet_names]
            
            if missing_sheets:
                self.add_error(f"缺少必要的工作表: {', '.join(missing_sheets)}")
                self.is_valid = False
                return False

            # 执行严格验证
            self._update_progress(30, "执行严格验证规则...")
            self._validate_strict_rules()

            # 执行警告级别的验证
            self._update_progress(60, "执行警告级别验证...")
            self._validate_warning_rules()

            # 验证监考员总场次限制
            self._update_progress(80, "验证监考员总场次限制...")
            self._validate_total_supervision_capacity()

            # 更新最终验证结果
            self.is_valid = not self.strict_validation_failed and len(self.errors) == 0

            # 如果验证失败，生成错误报告
            if not self.is_valid or self.warnings:
                self._generate_error_report()

            self._update_progress(100, "验证完成")
            return self.is_valid

        except Exception as e:
            error_msg = f"验证过程发生错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self.add_error(error_msg)
            self.is_valid = False
            self._update_progress(100, "验证失败")
            return False

    def _validate_strict_rules(self):
        """执行严格验证规则"""
        try:
            # 验证监考员设置
            if '监考员设置' in self.workbook.sheet_names:
                teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
                self._validate_teacher_basic_info(teacher_df)
                self._validate_session_limits(teacher_df)
                self._validate_data_format(teacher_df)

            # 验证考试科目设置
            if '考试科目设置' in self.workbook.sheet_names:
                self._validate_subject_sheet()

            # 验证考场设置
            if '考场设置' in self.workbook.sheet_names:
                self._validate_room_sheet()

        except Exception as e:
            logger.error(f"执行严格验证规则时发生错误: {str(e)}", exc_info=True)
            self.strict_validation_failed = True
            self.add_error(f"严格验证失败: {str(e)}")

    def _validate_warning_rules(self):
        """执行警告级别的验证规则"""
        try:
            self._check_subject_matching()
            self._check_exam_time_distribution()
            self._check_workload_distribution()
        except Exception as e:
            logger.warning(f"执行警告级别验证时发生错误: {str(e)}", exc_info=True)
            self.add_warning(f"警告级别验证过程出现异常: {str(e)}")

    def _validate_teacher_basic_info(self, teacher_df: pd.DataFrame):
        """验证监考员基本信息"""
        try:
            # 检查必要列
            required_columns = ['序号', '监考老师', '任教科目', '场次限制', '必监考科目', '不监考科目']
            missing_columns = [col for col in required_columns if col not in teacher_df.columns]
            if missing_columns:
                error_msg = f"监考员设置表缺少必要的列: {', '.join(missing_columns)}"
                self.add_error(error_msg)
                self.add_error_detail('监考员设置', 1, '列名', '', '缺少列', error_msg)
                self.strict_validation_failed = True
                return

            # 检查重复的监考员
            duplicates = teacher_df[teacher_df['监考老师'].duplicated()]['监考老师'].tolist()
            if duplicates:
                error_msg = f"存在重复的监考员: {', '.join(duplicates)}"
                self.add_error(error_msg)
                self.add_error_detail('监考员设置', 1, '监考老师', str(duplicates), '数据重复', error_msg)
                self.strict_validation_failed = True

            # 检查监考老师姓名格式
            invalid_names = teacher_df[
                ~teacher_df['监考老师'].astype(str).str.match(r'^[\u4e00-\u9fa5]{2,4}$')
            ]['监考老师'].tolist()
            if invalid_names:
                error_msg = f"监考老师姓名格式无效: {', '.join(map(str, invalid_names))}"
                self.add_error(error_msg)
                self.add_error_detail('监考员设置', 1, '监考老师', str(invalid_names), '格式错误', error_msg)
                self.strict_validation_failed = True

            # 检查必监考科目和不监考科目的重复
            for idx, row in teacher_df.iterrows():
                must_subjects = []
                avoid_subjects = []

                if pd.notna(row['必监考科目']) and str(row['必监考科目']) != '0':
                    must_subjects = [s.strip() for s in str(row['必监考科目']).split(',') if s.strip()]

                if pd.notna(row['不监考科目']) and str(row['不监考科目']) != '0':
                    avoid_subjects = [s.strip() for s in str(row['不监考科目']).split(',') if s.strip()]

                common_subjects = set(must_subjects) & set(avoid_subjects)
                if common_subjects:
                    error_msg = f"监考员{row['监考老师']}的必监考科目和不监考科目存在重复: {', '.join(common_subjects)}"
                    self.add_error(error_msg)
                    self.add_error_detail('监考员设置', idx+2, '监考科目', str(common_subjects), '科目重复', error_msg)
                    self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证监考员基本信息时发生错误: {str(e)}", exc_info=True)
            self.add_error(f"验证监考员基本信息失败: {str(e)}")
            self.strict_validation_failed = True

    def _validate_session_limits(self, teacher_df: pd.DataFrame):
        """验证场次限制"""
        try:
            # 检查场次限制的有效性
            invalid_limits = teacher_df[~teacher_df['场次限制'].apply(lambda x: isinstance(x, (int, float)) and x > 0)]
            if not invalid_limits.empty:
                for idx, row in invalid_limits.iterrows():
                    error_msg = f"监考员{row['监考老师']}的场次限制必须为正整数"
                    self.add_error(error_msg)
                    self.add_error_detail('监考员设置', idx+2, '场次限制', str(row['场次限制']), '数据格式错误', error_msg)
                    self.strict_validation_failed = True

            # 检查场次限制总和是否满足需求
            total_limits = teacher_df['场次限制'].sum()
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
            total_sessions = len(subject_df) * 2  # 每场考试需要2名监考员

            if total_limits < total_sessions:
                error_msg = f"监考员总场次限制({total_limits})小于所需监考总场次({total_sessions})"
                self.add_error(error_msg)
                self.add_error_detail('监考员设置', 1, '场次限制', str(total_limits), '场次不足', error_msg)
                self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证场次限制时发生错误: {str(e)}", exc_info=True)
            self.add_error(f"验证场次限制失败: {str(e)}")
            self.strict_validation_failed = True

    def _validate_data_format(self, teacher_df: pd.DataFrame):
        """验证数据格式"""
        try:
            # 检查序号的连续性
            expected_seq = range(1, len(teacher_df) + 1)
            actual_seq = teacher_df['序号'].tolist()
            if not all(a == e for a, e in zip(actual_seq, expected_seq)):
                error_msg = "序号不连续或不从1开始"
                self.add_error(error_msg)
                self.add_error_detail('监考员设置', 1, '序号', str(actual_seq), '序号错误', error_msg)
                self.strict_validation_failed = True

            # 检查任教科目格式
            for idx, row in teacher_df.iterrows():
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    if subject_str != '0' and subject_str.lower() != 'nan':
                        if not bool(re.match(r'^[\u4e00-\u9fa5,]+$', subject_str)):
                            error_msg = f"监考员{row['监考老师']}的任教科目格式无效"
                            self.add_error(error_msg)
                            self.add_error_detail('监考员设置', idx+2, '任教科目', subject_str, '格式错误', error_msg)
                            self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证数据格式时发生错误: {str(e)}", exc_info=True)
            self.add_error(f"验证数据格式失败: {str(e)}")
            self.strict_validation_failed = True

    def _validate_subject_sheet(self):
        """验证考试科目设置"""
        try:
            sheet = pd.read_excel(self.workbook, sheet_name='考试科目设置')
            
            # 检查必要的列
            required_columns = ['课程代码', '课程名称', '开始时间', '结束时间']
            missing_columns = [col for col in required_columns if col not in sheet.columns]
            if missing_columns:
                error_msg = f"考试科目设置表缺少必要的列: {', '.join(missing_columns)}"
                self.add_error(error_msg)
                self.add_error_detail('考试科目设置', 1, '列名', '', '缺少列', error_msg)
                return

            # 检查每行数据
            for idx, row in sheet.iterrows():
                # 检查空值
                if any(pd.isna(row[col]) for col in required_columns):
                    error_msg = f"考试科目设置表第{idx+2}行存在空值"
                    self.add_error(error_msg)
                    self.add_error_detail('考试科目设置', idx+2, '所有列', '', '存在空值', error_msg)
                    continue

                # 检查时间格式和逻辑
                try:
                    start_time = pd.to_datetime(row['开始时间'], format='%H:%M').time()
                    end_time = pd.to_datetime(row['结束时间'], format='%H:%M').time()
                    
                    if end_time <= start_time:
                        error_msg = f"考试科目设置表第{idx+2}行结束时间不能早于或等于开始时间"
                        self.add_error(error_msg)
                        self.add_error_detail('考试科目设置', idx+2, '时间', f"{start_time}-{end_time}", '时间错误', error_msg)
                except ValueError:
                    error_msg = f"考试科目设置表第{idx+2}行时间格式错误，应为HH:MM格式"
                    self.add_error(error_msg)
                    self.add_error_detail('考试科目设置', idx+2, '时间', f"{row['开始时间']}-{row['结束时间']}", '格式错误', error_msg)

            # 检查时间重叠
            exam_times = []
            for idx, row in sheet.iterrows():
                try:
                    start_time = pd.to_datetime(row['开始时间'], format='%H:%M').time()
                    end_time = pd.to_datetime(row['结束时间'], format='%H:%M').time()
                    
                    for prev_start, prev_end, prev_idx in exam_times:
                        if (start_time < prev_end and end_time > prev_start):
                            error_msg = f"考试科目设置表第{idx+2}行的考试时间与第{prev_idx+2}行时间重叠"
                            self.add_error(error_msg)
                            self.add_error_detail('考试科目设置', idx+2, '时间', f"{start_time}-{end_time}", '时间重叠', error_msg)
                    
                    exam_times.append((start_time, end_time, idx))
                except ValueError:
                    continue

        except Exception as e:
            logger.error(f"验证考试科目设置时发生错误: {str(e)}", exc_info=True)
            self.add_error(f"验证考试科目设置失败: {str(e)}")
            self.strict_validation_failed = True

    def _validate_room_sheet(self):
        """验证考场设置"""
        try:
            sheet = pd.read_excel(self.workbook, sheet_name='考场设置')
            
            if sheet.empty:
                self.add_error("考场设置表为空")
                return

            if '考场' not in sheet.columns:
                self.add_error("考场设置表缺少必要的列: 考场")
                return

            # 检查考场编号
            for idx, row in sheet.iterrows():
                if pd.isna(row['考场']):
                    error_msg = f"考场设置表第{idx+2}行考场编号为空"
                    self.add_error(error_msg)
                    self.add_error_detail('考场设置', idx+2, '考场', '', '数据为空', error_msg)

        except Exception as e:
            logger.error(f"验证考场设置时发生错误: {str(e)}", exc_info=True)
            self.add_error(f"验证考场设置失败: {str(e)}")
            self.strict_validation_failed = True

    def _validate_total_supervision_capacity(self):
        """验证监考员总场次限制是否满足考场总需求"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            room_df = pd.read_excel(self.workbook, sheet_name='考场设置')
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')

            # 计算总场次限制
            total_capacity = teacher_df['场次限制'].sum()

            # 计算所需监考员总数
            total_sessions = len(subject_df)
            rooms_per_session = len(room_df)
            total_required = total_sessions * rooms_per_session * 2  # 每个考场需要2名监考员

            if total_capacity < total_required:
                error_msg = f"监考员总场次限制({total_capacity})不足，需要{total_required}场次"
                self.add_error(error_msg)
                self.add_error_detail('监考员设置', 1, '场次限制', str(total_capacity), '场次不足', error_msg)
                self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证监考员总场次限制时发生错误: {str(e)}", exc_info=True)
            self.add_error(f"验证监考员总场次限制失败: {str(e)}")
            self.strict_validation_failed = True

    def _check_subject_matching(self):
        """检查监考员任教科目与考试科目的匹配度"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')

            exam_subjects = set(subject_df['课程名称'].unique())
            unmatch_count = 0

            for _, row in teacher_df.iterrows():
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    if subject_str != '0':
                        teacher_subjects = {s.strip() for s in subject_str.split(',') if s.strip()}
                        if not teacher_subjects & exam_subjects:
                            unmatch_count += 1
                            self.add_warning(
                                f"监考员{row['监考老师']}的任教科目({subject_str})与考试科目无匹配"
                            )

            if unmatch_count > len(teacher_df) / 3:  # 超过1/3的监考员不匹配
                self.add_warning("大量监考员的任教科目与考试科目不匹配，建议检查科目设置")

        except Exception as e:
            logger.warning(f"检查科目匹配时发生错误: {str(e)}", exc_info=True)

    def _check_exam_time_distribution(self):
        """检查考试时间分布是否合理"""
        try:
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
            
            # 转换时间格式
            subject_df['开始时间'] = pd.to_datetime(subject_df['开始时间'], format='%H:%M').dt.time
            subject_df['结束时间'] = pd.to_datetime(subject_df['结束时间'], format='%H:%M').dt.time
            
            # 检查时间分布
            time_slots = list(zip(subject_df['开始时间'], subject_df['结束时间']))
            max_concurrent = 0
            
            for i, (start1, end1) in enumerate(time_slots):
                concurrent = 1
                for j, (start2, end2) in enumerate(time_slots):
                    if i != j and (start1 <= start2 < end1 or start2 <= start1 < end2):
                        concurrent += 1
                max_concurrent = max(max_concurrent, concurrent)
            
            if max_concurrent > len(subject_df) / 2:
                self.add_warning(
                    f"考试时间分布过于集中，最多有{max_concurrent}场考试同时进行"
                )

        except Exception as e:
            logger.warning(f"检查考试时间分布时发生错误: {str(e)}", exc_info=True)

    def _check_workload_distribution(self):
        """检查监考员工作量分布是否合理"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            
            # 计算场次限制的统计信息
            avg_limit = teacher_df['场次限制'].mean()
            std_limit = teacher_df['场次限制'].std()
            
            # 检查异常值
            for _, row in teacher_df.iterrows():
                if abs(row['场次限制'] - avg_limit) > 2 * std_limit:
                    self.add_warning(
                        f"监考员{row['监考老师']}的场次限制({row['场次限制']})与平均值({avg_limit:.1f})差异较大"
                    )

        except Exception as e:
            logger.warning(f"检查工作量分布时发生错误: {str(e)}", exc_info=True)
