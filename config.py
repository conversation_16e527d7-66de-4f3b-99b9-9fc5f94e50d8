# 监考安排系统生产环境配置 (Debian)
# 自动生成于 2024-04-20

import os
import secrets

class Config:
    # 配置文件路径
    INSTANCE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
    SECRET_KEY_FILE = os.path.join(INSTANCE_PATH, 'secret_key')

    @classmethod
    def get_or_generate_secret_key(cls):
        """获取或生成SECRET_KEY"""
        # 确保instance目录存在
        os.makedirs(cls.INSTANCE_PATH, exist_ok=True)
        
        # 如果密钥文件存在，读取它
        if os.path.exists(cls.SECRET_KEY_FILE):
            with open(cls.SECRET_KEY_FILE, 'r') as f:
                return f.read().strip()
        
        # 生成新的密钥
        secret_key = secrets.token_hex(32)  # 生成一个256位的随机密钥
        
        # 保存密钥到文件
        with open(cls.SECRET_KEY_FILE, 'w') as f:
            f.write(secret_key)
        
        return secret_key

    # 基础配置
    SQLALCHEMY_DATABASE_URI = 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 最大10MB
    ALLOWED_EXTENSIONS = {'xlsx'}

    # 临时文件目录
    TMP_DIR = '/tmp/jiankao'

    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'app.log'
    LOG_DIR = 'logs'

    # 安全配置
    SESSION_COOKIE_SECURE = True  # 启用HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    REMEMBER_COOKIE_SECURE = True  # 启用HTTPS
    REMEMBER_COOKIE_HTTPONLY = True
    PREFERRED_URL_SCHEME = 'https'  # 使用HTTPS
    
    # CSRF配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_CHECK_DEFAULT = True
    WTF_CSRF_TIME_LIMIT = 3600
    WTF_CSRF_SSL_STRICT = True

    # 缓存目录配置
    CACHE_DIR = os.path.join(TMP_DIR, 'cache')

    # 任务处理配置
    DEFAULT_TIME_LIMIT = 300  # 默认求解时间限制(秒)
    MAX_DURATION_DIFF = 1.0  # 同组教师间允许的最大监考时长差异(小时)

    # Gunicorn配置
    BIND = '0.0.0.0:5000'
    WORKERS = 4
    TIMEOUT = 120
    ACCESS_LOG = os.path.join(LOG_DIR, 'gunicorn_access.log')
    ERROR_LOG = os.path.join(LOG_DIR, 'gunicorn_error.log')

    # Redis配置
    REDIS_HOST = 'localhost'
    REDIS_PORT = 6379
    REDIS_DB = 0
    REDIS_PASSWORD = None  # 如果有密码，在这里设置
    
    @staticmethod
    def init_redis():
        """初始化Redis连接"""
        import redis
        return redis.Redis(
            host=Config.REDIS_HOST,
            port=Config.REDIS_PORT,
            db=Config.REDIS_DB,
            password=Config.REDIS_PASSWORD,
            decode_responses=True
        )
