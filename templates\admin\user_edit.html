{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <h1 class="h2 mb-4">编辑用户</h1>

    <div class="card">
        <div class="card-body">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" required>
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label">邮箱</label>
                    <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                </div>

                <div class="mb-3">
                    <label for="role" class="form-label">角色</label>
                    <select class="form-select" id="role" name="role" onchange="handleRoleChange()">
                        <option value="user" {% if user.role == 'user' %}selected{% endif %}>普通用户</option>
                        <option value="vip" {% if user.role == 'vip' %}selected{% endif %}>VIP用户</option>
                        <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>管理员</option>
                    </select>
                </div>

                <div class="mb-3" id="vipExpiryContainer" style="display: none;">
                    <label for="vip_expiry" class="form-label">VIP有效期</label>
                    <input type="date" class="form-control" id="vip_expiry" name="vip_expiry"
                           value="{{ user.expiry_date.strftime('%Y-%m-%d') if user.expiry_date else '' }}">
                    <small class="form-text text-muted">留空表示永久有效。如果用户从普通用户升级为VIP，默认有效期为1个月。</small>
                </div>

                <div class="mb-3">
                    <label for="task_limit" class="form-label">任务限制</label>
                    <input type="number" class="form-control" id="task_limit" name="task_limit" value="{{ user.task_limit }}" min="1" required>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if user.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">账号启用</label>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="new_password" class="form-label">新密码（留空表示不修改）</label>
                    <input type="password" class="form-control" id="new_password" name="new_password">
                </div>

                <div class="mb-3">
                    <button type="submit" class="btn btn-primary">保存更改</button>
                    <a href="{{ url_for('admin_users') }}" class="btn btn-secondary">返回</a>
                </div>
            </form>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            用户信息
        </div>
        <div class="card-body">
            <p><strong>注册时间：</strong> {{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
            <p><strong>最后登录：</strong> {{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录' }}</p>
            <p><strong>总任务数：</strong> {{ user.tasks.count() }}</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function handleRoleChange() {
    const roleSelect = document.getElementById('role');
    const vipExpiryContainer = document.getElementById('vipExpiryContainer');
    const vipExpiryInput = document.getElementById('vip_expiry');
    const taskLimitInput = document.getElementById('task_limit');

    if (roleSelect.value === 'vip') {
        vipExpiryContainer.style.display = 'block';
        // 如果是从普通用户升级到VIP且没有设置过期时间，则设置默认一个月的有效期
        if (!vipExpiryInput.value) {
            const defaultDate = new Date();
            defaultDate.setMonth(defaultDate.getMonth() + 1);
            vipExpiryInput.value = defaultDate.toISOString().split('T')[0];
        }
        // VIP用户默认6个任务
        taskLimitInput.value = '6';
    } else if (roleSelect.value === 'admin') {
        vipExpiryContainer.style.display = 'none';
        vipExpiryInput.value = '';
        // 管理员默认999个任务
        taskLimitInput.value = '999';
    } else {
        vipExpiryContainer.style.display = 'none';
        vipExpiryInput.value = '';
        // 普通用户默认1个任务
        taskLimitInput.value = '1';
    }
}

// 页面加载时执行一次
document.addEventListener('DOMContentLoaded', handleRoleChange);
</script>
{% endblock %}