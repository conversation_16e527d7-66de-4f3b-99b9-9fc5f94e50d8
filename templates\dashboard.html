{% extends "base.html" %}

{% block title %}{{ title }} - 均程通用监考安排{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Layout */
    .dashboard-header {
        background-color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        box-shadow: var(--card-shadow);
    }

    /* Task Cards */
    .task-card {
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .task-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .task-card .card-header {
        padding: 1rem 1.25rem;
    }

    .task-card .card-title {
        color: #2c3e50;
        font-size: 1rem;
        font-weight: 600;
    }

    .task-card .badge {
        padding: 0.5em 0.8em;
        font-weight: 500;
        font-size: 0.75rem;
    }

    .task-card .card-body {
        padding: 1rem 1.25rem;
    }

    .task-card .card-footer {
        padding: 0.75rem 1.25rem;
        background-color: rgba(0, 0, 0, 0.02);
    }

    /* Task Meta Information */
    .task-meta {
        display: flex;
        align-items: center;
        color: var(--text-secondary);
        font-size: 0.85rem;
    }

    .task-meta i {
        margin-right: 5px;
    }

    .task-meta-item {
        margin-right: 15px;
    }

    /* Progress Bars */
    .progress {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 10px;
        overflow: hidden;
    }

    .progress-bar {
        border-radius: 10px;
    }

    /* Action Buttons */
    .task-action-btn {
        border-radius: 50px;
        padding: 6px 16px;
        font-size: 0.85rem;
        transition: all 0.2s;
    }

    .task-action-btn:hover {
        transform: translateY(-2px);
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .empty-state-icon {
        font-size: 3rem;
        color: #dee2e6;
        margin-bottom: 1.5rem;
    }

    /* Dashboard Stats */
    .dashboard-stats {
        display: flex;
        margin-bottom: 24px;
    }

    .stat-card {
        flex: 1;
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-right: 15px;
        box-shadow: var(--card-shadow);
        transition: all 0.3s ease;
    }

    .stat-card:last-child {
        margin-right: 0;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    /* Stat Icons */
    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
        font-size: 1.5rem;
        color: white;
    }

    .stat-pending {
        background-color: #6c757d;
    }

    .stat-processing {
        background-color: #0d6efd;
    }

    .stat-completed {
        background-color: #198754;
    }

    .stat-failed {
        background-color: #dc3545;
    }

    /* Stat Text */
    .stat-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin: 0;
        line-height: 1;
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-top: 5px;
    }

    /* Task Filters */
    .task-filter {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .filter-label {
        margin-right: 10px;
        color: var(--text-secondary);
    }

    .filter-btn {
        background: white;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 20px;
        padding: 5px 15px;
        margin-right: 8px;
        font-size: 0.85rem;
        color: var(--text-secondary);
        transition: all 0.2s;
    }

    .filter-btn:hover,
    .filter-btn.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .dashboard-stats {
            flex-direction: column;
        }

        .stat-card {
            margin-right: 0;
            margin-bottom: 15px;
        }

        .task-filter {
            flex-wrap: wrap;
        }

        .filter-btn {
            margin-bottom: 8px;
        }

        .task-card {
            margin-bottom: 1rem;
        }

        .task-card .card-header {
            padding: 0.75rem 1rem;
        }

        .task-card .card-body {
            padding: 0.75rem 1rem;
        }

        .task-card .card-footer {
            padding: 0.75rem 1rem;
        }
    }
</style>
{% endblock %}

{# Dashboard Content #}
{% block content %}
{% include 'partials/dashboard_content.html' %}
{% endblock %}

{# JavaScript #}
{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Task filtering
        const filterButtons = document.querySelectorAll('.filter-btn');
        const taskItems = document.querySelectorAll('.task-item');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');

                // Filter tasks
                taskItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-status') === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    });
</script>
{% endblock %}
