{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">测试日志获取</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="taskId" class="form-label">任务ID</label>
                        <input type="number" class="form-control" id="taskId" value="63">
                    </div>
                    <button type="button" class="btn btn-primary" id="fetchLogs">获取日志</button>
                    
                    <hr>
                    
                    <div class="mt-3">
                        <h6>任务状态</h6>
                        <div class="d-flex align-items-center mb-3">
                            <span class="me-2" id="progressValue">0%</span>
                            <div class="progress" style="flex-grow: 1;">
                                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <span class="ms-2 badge bg-secondary" id="statusBadge">未知</span>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>日志内容 <span class="badge bg-secondary" id="logCount">0 条</span></h6>
                        <div class="bg-light p-3 rounded" style="height: 400px; overflow-y: auto; font-family: monospace;">
                            <div id="logContent">
                                <p class="text-muted">等待日志输出...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 获取日志按钮点击事件
    $('#fetchLogs').click(function() {
        const taskId = $('#taskId').val();
        if (!taskId) {
            alert('请输入任务ID');
            return;
        }
        
        // 显示加载状态
        $(this).html('<i class="fas fa-spinner fa-spin"></i> 加载中...');
        $(this).prop('disabled', true);
        
        // 获取日志
        $.ajax({
            url: '/task/' + taskId + '/progress',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                console.log('获取日志成功:', data);
                
                // 更新进度条
                $('.progress-bar').css('width', data.progress + '%');
                $('.progress-bar').attr('aria-valuenow', data.progress);
                $('#progressValue').text(data.progress + '%');
                
                // 更新状态标签
                $('#statusBadge').removeClass().addClass('badge bg-' + data.status_color).text(data.status_display);
                
                // 更新日志内容
                if (data.logs && data.logs.length > 0) {
                    $('#logCount').text(data.logs.length + ' 条');
                    
                    let logHtml = '';
                    data.logs.forEach(log => {
                        // 判断是否是进度行
                        if (log.includes('进度:')) {
                            logHtml += `<p class="log-line" style="background-color: rgba(13, 110, 253, 0.1); padding: 4px 8px; border-radius: 4px; margin: 4px 0;">
                                <span style="color: #0d6efd; font-weight: bold;">${log}</span>
                            </p>`;
                        } 
                        // 判断是否是错误行
                        else if (log.includes('错误') || log.includes('Error') || log.includes('error') || log.includes('失败')) {
                            logHtml += `<p class="log-line" style="color: #dc3545;">
                                <i class="fas fa-exclamation-circle"></i> ${log}
                            </p>`;
                        }
                        // 判断是否是成功行
                        else if (log.includes('成功') || log.includes('完成')) {
                            logHtml += `<p class="log-line" style="color: #198754;">
                                <i class="fas fa-check-circle"></i> ${log}
                            </p>`;
                        }
                        // 普通日志行
                        else {
                            logHtml += `<p class="log-line">${log}</p>`;
                        }
                    });
                    
                    $('#logContent').html(logHtml);
                } else {
                    $('#logContent').html('<p class="text-muted">暂无日志输出...</p>');
                    $('#logCount').text('0 条');
                }
            },
            error: function(xhr, status, error) {
                console.error('获取日志失败:', error);
                $('#logContent').html('<p class="text-danger">获取日志失败: ' + error + '</p>');
            },
            complete: function() {
                // 恢复按钮状态
                $('#fetchLogs').html('获取日志');
                $('#fetchLogs').prop('disabled', false);
            }
        });
    });
});
</script>
{% endblock %}
