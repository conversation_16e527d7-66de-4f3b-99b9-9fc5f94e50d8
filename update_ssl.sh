#!/bin/bash
# SSL证书更新脚本
# 作者：AI助手
# 日期：2024年4月20日

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
if [ "$(id -u)" != "0" ]; then
    log_error "此脚本需要root权限运行"
    log_info "请使用 'sudo $0' 重新运行"
    exit 1
fi

# 检查certbot是否已安装
if ! command -v certbot &> /dev/null; then
    log_error "未找到certbot命令，请先安装certbot"
    log_info "可以使用 'apt-get install -y certbot python3-certbot-nginx' 安装"
    exit 1
fi

# 获取域名
read -p "请输入您的域名: " DOMAIN_NAME

if [ -z "$DOMAIN_NAME" ]; then
    log_error "域名不能为空"
    exit 1
fi

log_step "正在更新SSL证书..."

# 使用Certbot更新证书
certbot --nginx -d $DOMAIN_NAME --non-interactive --agree-tos --redirect

if [ $? -eq 0 ]; then
    log_info "SSL证书更新成功！"
    
    # 确保自动续期已启用
    log_step "确保证书自动续期已启用..."
    systemctl enable certbot.timer
    systemctl restart certbot.timer
    
    log_info "证书将自动续期"
    
    # 重启Nginx
    log_step "重启Nginx服务..."
    systemctl restart nginx
    
    log_info "Nginx已重启，SSL证书已生效"
else
    log_error "SSL证书更新失败，请检查域名是否正确并确保DNS已正确设置"
fi
