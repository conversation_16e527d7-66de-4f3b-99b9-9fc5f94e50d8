#!/usr/bin/env python3
"""
启发式修复策略模块

专门针对监考安排中的约束违反问题设计修复规则
目标：在不破坏现有合理分配的基础上，修复具体的约束违反
"""

from collections import defaultdict, Counter
import logging
from typing import List, Dict, Set, Tuple, Optional

class HeuristicRepairEngine:
    """启发式修复引擎"""
    
    def __init__(self, scheduler):
        self.scheduler = scheduler
        self.teachers = scheduler.teachers
        self.subjects = scheduler.subjects
        self.rooms = scheduler.rooms
        self.subject_map = scheduler.subject_map
        
    def repair_schedule(self, schedule):
        """
        主修复函数：按优先级修复各种约束违反
        
        修复优先级：
        1. 场次限制违反（最高优先级）
        2. 考场缺人问题
        3. 必监考科目违反
        4. 工作量不平衡问题
        """
        print("\n=== 启动启发式修复策略 ===")
        
        original_fitness = schedule.calculate_fitness(self.scheduler)
        print(f"修复前适应度分数: {original_fitness:.2f}")
        
        # 修复阶段1：场次限制违反
        self._repair_session_limit_violations(schedule)
        
        # 修复阶段2：考场缺人问题
        self._repair_missing_positions(schedule)
        
        # 修复阶段3：必监考科目违反
        self._repair_must_subject_violations(schedule)
        
        # 修复阶段4：工作量平衡优化
        self._repair_workload_imbalance(schedule)
        
        final_fitness = schedule.calculate_fitness(self.scheduler)
        print(f"修复后适应度分数: {final_fitness:.2f}")
        print(f"适应度改进: {final_fitness - original_fitness:.2f}")
        
        return schedule
    
    def _repair_session_limit_violations(self, schedule):
        """修复场次限制违反问题"""
        print("\n修复阶段1: 处理场次限制违反")
        
        # 识别违反场次限制的教师
        violations = self._identify_session_violations(schedule)
        
        if not violations:
            print("  ✅ 无场次限制违反")
            return
        
        for violation_type, teachers in violations.items():
            if violation_type == 'over_assigned':
                print(f"  处理过度分配的教师: {[t.name for t in teachers]}")
                self._fix_over_assigned_teachers(schedule, teachers)
            elif violation_type == 'under_assigned':
                print(f"  处理分配不足的教师: {[t.name for t in teachers]}")
                self._fix_under_assigned_teachers(schedule, teachers)
    
    def _repair_missing_positions(self, schedule):
        """修复考场缺人问题"""
        print("\n修复阶段2: 处理考场缺人问题")
        
        # 识别缺人的考场位置
        missing_positions = self._identify_missing_positions(schedule)
        
        if not missing_positions:
            print("  ✅ 无考场缺人问题")
            return
        
        print(f"  发现 {len(missing_positions)} 处缺人问题")
        
        for subject, room, missing_count in missing_positions:
            print(f"  尝试修复: {subject}({room}) 缺{missing_count}人")
            success = self._fill_missing_position(schedule, subject, room, missing_count)
            if success:
                print(f"    ✅ 成功修复")
            else:
                print(f"    ❌ 暂无可行解决方案")
    
    def _repair_must_subject_violations(self, schedule):
        """修复必监考科目违反"""
        print("\n修复阶段3: 处理必监考科目违反")
        
        violations = self._identify_must_subject_violations(schedule)
        
        if not violations:
            print("  ✅ 无必监考科目违反")
            return
        
        for teacher, missing_subjects in violations:
            print(f"  修复 {teacher.name} 的必监考科目: {missing_subjects}")
            self._assign_must_subjects(schedule, teacher, missing_subjects)
    
    def _repair_workload_imbalance(self, schedule):
        """修复工作量不平衡问题"""
        print("\n修复阶段4: 优化工作量平衡")
        
        # 计算工作量分布
        workload_stats = self._calculate_workload_distribution(schedule)
        
        if workload_stats['std_dev'] < 1.0:
            print("  ✅ 工作量分布已相对平衡")
            return
        
        print(f"  当前工作量标准差: {workload_stats['std_dev']:.2f}")
        
        # 尝试平衡工作量
        self._balance_workload(schedule, workload_stats)
    
    def _identify_session_violations(self, schedule):
        """识别场次限制违反"""
        violations = {'over_assigned': [], 'under_assigned': []}
        
        for teacher in self.teachers:
            assignments = schedule.teacher_assignments.get(teacher.name, [])
            actual_sessions = len(assignments)
            
            if actual_sessions > teacher.max_sessions:
                violations['over_assigned'].append(teacher)
            elif actual_sessions < teacher.max_sessions:
                violations['under_assigned'].append(teacher)
        
        return violations
    
    def _identify_missing_positions(self, schedule):
        """识别缺人的考场位置"""
        missing_positions = []
        
        for room in self.rooms:
            for subject, required_count in room.subject_requirements.items():
                actual_assignments = schedule.assignments.get((subject, room.name), [])
                actual_count = len(actual_assignments)
                
                if actual_count < required_count:
                    missing_count = required_count - actual_count
                    missing_positions.append((subject, room.name, missing_count))
        
        return missing_positions
    
    def _identify_must_subject_violations(self, schedule):
        """识别必监考科目违反"""
        violations = []
        
        for teacher in self.teachers:
            if not teacher.must_subjects:
                continue
                
            assignments = schedule.teacher_assignments.get(teacher.name, [])
            assigned_subjects = {subject for subject, _ in assignments}
            
            missing_subjects = teacher.must_subjects - assigned_subjects
            if missing_subjects:
                violations.append((teacher, missing_subjects))
        
        return violations
    
    def _fix_over_assigned_teachers(self, schedule, over_assigned_teachers):
        """修复过度分配的教师"""
        for teacher in over_assigned_teachers:
            assignments = schedule.teacher_assignments.get(teacher.name, [])
            excess_count = len(assignments) - teacher.max_sessions
            
            # 选择要移除的分配（优先移除非必监考科目）
            removable_assignments = []
            for subject, room in assignments:
                if subject not in teacher.must_subjects:
                    removable_assignments.append((subject, room))
            
            # 如果没有可移除的非必监考科目，则从所有分配中选择
            if len(removable_assignments) < excess_count:
                removable_assignments = assignments.copy()
            
            # 移除多余的分配
            for i in range(min(excess_count, len(removable_assignments))):
                subject, room = removable_assignments[i]
                self._remove_assignment(schedule, subject, room, teacher.name)
                print(f"    移除分配: {teacher.name} -> {subject}({room})")
    
    def _fix_under_assigned_teachers(self, schedule, under_assigned_teachers):
        """修复分配不足的教师"""
        for teacher in under_assigned_teachers:
            assignments = schedule.teacher_assignments.get(teacher.name, [])
            needed_count = teacher.max_sessions - len(assignments)
            
            # 寻找可分配的位置
            for _ in range(needed_count):
                best_assignment = self._find_best_assignment_for_teacher(schedule, teacher)
                if best_assignment:
                    subject, room = best_assignment
                    schedule.add_assignment(subject, room, teacher.name)
                    print(f"    添加分配: {teacher.name} -> {subject}({room})")
                else:
                    print(f"    无法为 {teacher.name} 找到更多合适的分配")
                    break
    
    def _fill_missing_position(self, schedule, subject, room, missing_count):
        """填补缺少的考场位置"""
        filled_count = 0
        
        for _ in range(missing_count):
            # 寻找最佳候选教师
            best_teacher = self._find_best_teacher_for_position(schedule, subject, room)
            
            if best_teacher:
                # 检查是否需要进行交换
                if len(schedule.teacher_assignments.get(best_teacher.name, [])) >= best_teacher.max_sessions:
                    # 尝试交换分配
                    swap_success = self._try_swap_assignment(schedule, best_teacher, subject, room)
                    if swap_success:
                        filled_count += 1
                        print(f"    通过交换分配: {best_teacher.name} -> {subject}({room})")
                else:
                    # 直接分配
                    schedule.add_assignment(subject, room, best_teacher.name)
                    filled_count += 1
                    print(f"    直接分配: {best_teacher.name} -> {subject}({room})")
            else:
                break
        
        return filled_count > 0
    
    def _assign_must_subjects(self, schedule, teacher, missing_subjects):
        """为教师分配必监考科目"""
        for subject in missing_subjects:
            # 寻找该科目的空缺位置
            available_rooms = self._find_available_rooms_for_subject(schedule, subject, teacher)
            
            if available_rooms:
                best_room = available_rooms[0]  # 选择第一个可用考场
                
                # 检查是否需要交换
                current_assignments = len(schedule.teacher_assignments.get(teacher.name, []))
                if current_assignments >= teacher.max_sessions:
                    # 尝试用非必监考科目进行交换
                    swap_success = self._try_swap_for_must_subject(schedule, teacher, subject, best_room)
                    if swap_success:
                        print(f"    通过交换满足必监考: {teacher.name} -> {subject}({best_room})")
                else:
                    # 直接分配
                    schedule.add_assignment(subject, best_room, teacher.name)
                    print(f"    直接分配必监考: {teacher.name} -> {subject}({best_room})")
    
    def _find_best_assignment_for_teacher(self, schedule, teacher):
        """为教师寻找最佳分配"""
        candidates = []
        
        for subject in self.subjects:
            # 检查教师是否可以监考该科目
            if (subject.name in teacher.forbidden_subjects or
                not self._check_time_availability(schedule, teacher, subject.name)):
                continue
            
            for room in self.rooms:
                if (subject.name in room.subject_requirements and
                    room.name not in teacher.forbidden_rooms and
                    (not teacher.must_rooms or room.name in teacher.must_rooms)):
                    
                    # 检查是否有空缺
                    current_assignments = schedule.assignments.get((subject.name, room.name), [])
                    required_count = room.subject_requirements[subject.name]
                    
                    if len(current_assignments) < required_count:
                        # 计算分配的价值分数
                        score = self._calculate_assignment_value(teacher, subject.name, room.name)
                        candidates.append((subject.name, room.name, score))
        
        if candidates:
            # 返回评分最高的分配
            candidates.sort(key=lambda x: x[2], reverse=True)
            return (candidates[0][0], candidates[0][1])
        
        return None
    
    def _find_best_teacher_for_position(self, schedule, subject, room):
        """为特定位置寻找最佳教师"""
        candidates = []
        
        for teacher in self.teachers:
            # 基本约束检查
            if (subject in teacher.forbidden_subjects or
                room in teacher.forbidden_rooms or
                (teacher.must_rooms and room not in teacher.must_rooms) or
                not self._check_time_availability(schedule, teacher, subject)):
                continue
            
            # 计算教师适合度
            suitability_score = self._calculate_teacher_suitability(teacher, subject, room)
            candidates.append((teacher, suitability_score))
        
        if candidates:
            # 返回适合度最高的教师
            candidates.sort(key=lambda x: x[1], reverse=True)
            return candidates[0][0]
        
        return None
    
    def _try_swap_assignment(self, schedule, teacher, target_subject, target_room):
        """尝试通过交换来实现分配"""
        current_assignments = schedule.teacher_assignments.get(teacher.name, [])
        
        # 寻找可以交换的分配（优先选择非必监考科目）
        for current_subject, current_room in current_assignments:
            if current_subject not in teacher.must_subjects:
                # 尝试找其他教师来接替这个位置
                replacement_teacher = self._find_replacement_teacher(
                    schedule, current_subject, current_room, teacher
                )
                
                if replacement_teacher:
                    # 执行交换
                    self._remove_assignment(schedule, current_subject, current_room, teacher.name)
                    schedule.add_assignment(current_subject, current_room, replacement_teacher.name)
                    schedule.add_assignment(target_subject, target_room, teacher.name)
                    return True
        
        return False
    
    def _find_replacement_teacher(self, schedule, subject, room, excluded_teacher):
        """寻找替代教师"""
        for teacher in self.teachers:
            if (teacher.name == excluded_teacher.name or
                subject in teacher.forbidden_subjects or
                room in teacher.forbidden_rooms or
                (teacher.must_rooms and room not in teacher.must_rooms) or
                not self._check_time_availability(schedule, teacher, subject)):
                continue
            
            # 检查教师是否还有空余场次
            current_sessions = len(schedule.teacher_assignments.get(teacher.name, []))
            if current_sessions < teacher.max_sessions:
                return teacher
        
        return None
    
    def _calculate_assignment_value(self, teacher, subject, room):
        """计算分配的价值分数"""
        score = 0
        
        # 必监考科目权重最高
        if subject in teacher.must_subjects:
            score += 100
        
        # 必监考考场权重
        if room in teacher.must_rooms:
            score += 50
        
        # 任教科目匹配
        if subject == teacher.teaching_subject:
            score += 30
        
        # 工作量平衡
        current_sessions = len(getattr(teacher, 'current_assignments', []))
        remaining_sessions = teacher.max_sessions - current_sessions
        score += remaining_sessions * 5
        
        return score
    
    def _calculate_teacher_suitability(self, teacher, subject, room):
        """计算教师对特定位置的适合度"""
        score = 0
        
        # 必监考科目最高优先级
        if subject in teacher.must_subjects:
            score += 1000
        
        # 必监考考场优先级
        if room in teacher.must_rooms:
            score += 500
        
        # 任教科目匹配
        if subject == teacher.teaching_subject:
            score += 100
        
        # 当前工作量（越少越优先）
        current_sessions = len(getattr(teacher, 'current_assignments', []))
        score += (teacher.max_sessions - current_sessions) * 10
        
        return score
    
    def _check_time_availability(self, schedule, teacher, subject):
        """检查教师时间可用性"""
        subject_obj = self.subject_map[subject]
        new_time = (subject_obj.start_time, subject_obj.end_time)
        
        assignments = schedule.teacher_assignments.get(teacher.name, [])
        for assigned_subject, _ in assignments:
            assigned_subject_obj = self.subject_map[assigned_subject]
            existing_time = (assigned_subject_obj.start_time, assigned_subject_obj.end_time)
            
            if self._is_time_overlap(new_time, existing_time):
                return False
        
        return True
    
    def _remove_assignment(self, schedule, subject, room, teacher_name):
        """从调度方案中移除指定分配"""
        key = (subject, room)
        if key in schedule.assignments:
            teachers = schedule.assignments[key]
            if teacher_name in teachers:
                teachers.remove(teacher_name)
                if not teachers:
                    del schedule.assignments[key]
        
        if teacher_name in schedule.teacher_assignments:
            teacher_assignments = schedule.teacher_assignments[teacher_name]
            if (subject, room) in teacher_assignments:
                teacher_assignments.remove((subject, room))
                if not teacher_assignments:
                    del schedule.teacher_assignments[teacher_name]
    
    def _calculate_workload_distribution(self, schedule):
        """计算工作量分布统计"""
        workloads = []
        for teacher in self.teachers:
            assignments = schedule.teacher_assignments.get(teacher.name, [])
            workloads.append(len(assignments))
        
        if not workloads:
            return {'mean': 0, 'std_dev': 0, 'min': 0, 'max': 0}
        
        mean_workload = sum(workloads) / len(workloads)
        variance = sum((w - mean_workload) ** 2 for w in workloads) / len(workloads)
        std_dev = variance ** 0.5
        
        return {
            'mean': mean_workload,
            'std_dev': std_dev,
            'min': min(workloads),
            'max': max(workloads)
        }
    
    def _balance_workload(self, schedule, workload_stats):
        """平衡工作量分布"""
        # 识别工作量过高和过低的教师
        overloaded_teachers = []
        underloaded_teachers = []
        
        mean_workload = workload_stats['mean']
        
        for teacher in self.teachers:
            assignments = schedule.teacher_assignments.get(teacher.name, [])
            current_workload = len(assignments)
            
            if current_workload > mean_workload + 1:
                overloaded_teachers.append((teacher, current_workload))
            elif current_workload < mean_workload - 1 and current_workload < teacher.max_sessions:
                underloaded_teachers.append((teacher, current_workload))
        
        # 尝试在过载和不足的教师间进行调整
        for overloaded_teacher, _ in overloaded_teachers:
            for underloaded_teacher, _ in underloaded_teachers:
                success = self._try_transfer_assignment(schedule, overloaded_teacher, underloaded_teacher)
                if success:
                    print(f"    工作量平衡调整: {overloaded_teacher.name} -> {underloaded_teacher.name}")
                    break
    
    def _try_transfer_assignment(self, schedule, from_teacher, to_teacher):
        """尝试在教师间转移分配"""
        from_assignments = schedule.teacher_assignments.get(from_teacher.name, [])
        
        # 寻找可转移的分配（非必监考科目优先）
        for subject, room in from_assignments:
            if (subject not in from_teacher.must_subjects and
                subject not in to_teacher.forbidden_subjects and
                room not in to_teacher.forbidden_rooms and
                (not to_teacher.must_rooms or room in to_teacher.must_rooms) and
                self._check_time_availability(schedule, to_teacher, subject)):
                
                # 执行转移
                self._remove_assignment(schedule, subject, room, from_teacher.name)
                schedule.add_assignment(subject, room, to_teacher.name)
                return True
        
        return False
    
    @staticmethod
    def _is_time_overlap(slot1, slot2):
        """检查两个时间段是否重叠"""
        start1, end1 = slot1
        start2, end2 = slot2
        return max(start1, start2) < min(end1, end2)
    
    def _find_available_rooms_for_subject(self, schedule, subject, teacher):
        """为教师和科目寻找可用考场"""
        available_rooms = []
        
        for room in self.rooms:
            if (subject in room.subject_requirements and
                room.name not in teacher.forbidden_rooms and
                (not teacher.must_rooms or room.name in teacher.must_rooms)):
                
                # 检查是否有空缺
                current_assignments = schedule.assignments.get((subject, room.name), [])
                required_count = room.subject_requirements[subject]
                
                if len(current_assignments) < required_count:
                    available_rooms.append(room.name)
        
        return available_rooms
    
    def _try_swap_for_must_subject(self, schedule, teacher, must_subject, target_room):
        """为必监考科目尝试交换分配"""
        current_assignments = schedule.teacher_assignments.get(teacher.name, [])
        
        # 寻找非必监考科目进行交换
        for current_subject, current_room in current_assignments:
            if current_subject not in teacher.must_subjects:
                # 移除当前分配，添加必监考科目分配
                self._remove_assignment(schedule, current_subject, current_room, teacher.name)
                schedule.add_assignment(must_subject, target_room, teacher.name)
                return True
        
        return False


# 集成函数：将启发式修复集成到主程序
def integrate_heuristic_repair(scheduler, schedule):
    """
    将启发式修复集成到调度器中
    
    使用方法：
    在main.py的schedule_exams方法中，在返回最终结果前调用：
    
    # 启发式修复
    from heuristic_repair import integrate_heuristic_repair
    self.best_schedule = integrate_heuristic_repair(self, self.best_schedule)
    """
    print("\n=== 启动启发式修复策略 ===")
    
    repair_engine = HeuristicRepairEngine(scheduler)
    repaired_schedule = repair_engine.repair_schedule(schedule)
    
    return repaired_schedule


if __name__ == "__main__":
    print("启发式修复策略模块")
    print("使用说明：")
    print("1. 将此文件放在与main.py同目录下")
    print("2. 在main.py的schedule_exams方法中集成修复调用")
    print("3. 预期效果：修复具体的约束违反，提高适应度分数") 