import pandas as pd

print("=== 深入分析约束违反问题 ===")

# 读取原始数据
df_teachers = pd.read_excel('监考安排.xlsx', sheet_name='监考员设置')
df_subjects = pd.read_excel('监考安排.xlsx', sheet_name='考试科目设置')
df_rooms = pd.read_excel('监考安排.xlsx', sheet_name='考场设置')

# 读取结果数据
df_result = pd.read_excel('监考安排_20250603_213212.xlsx', sheet_name='监考员安排')
df_room_result = pd.read_excel('监考安排_20250603_213212.xlsx', sheet_name='考场安排')

# 检查丁继顺的情况
print("1. 丁继顺的约束设置:")
ding_row = df_teachers[df_teachers['监考老师'] == '丁继顺']
if len(ding_row) > 0:
    row = ding_row.iloc[0]
    print(f"   必监考科目: {row['必监考科目']}")
    print(f"   必监考考场: {row['必监考考场']}")
    print(f"   场次限制: {row['场次限制']}")

# 检查生物科目的时间
biology_time = df_subjects[df_subjects['课程名称'] == '生物']
print(f"\n2. 生物科目时间:")
if len(biology_time) > 0:
    row = biology_time.iloc[0]
    print(f"   时间: {row['开始时间']} - {row['结束时间']}")

# 检查丁继顺的实际安排
print(f"\n3. 丁继顺的实际安排:")
ding_result = df_result[df_result['监考老师'] == '丁继顺']
if len(ding_result) > 0:
    row = ding_result.iloc[0]
    print(f"   实际场次: {row['实际安排次数']}")
    
    # 检查所有科目安排及时间
    subject_assignments = []
    subject_columns = ['数学', '语文', '英语', '物理', '化学', '生物', '政史', '地理']
    for subject in subject_columns:
        if subject in df_result.columns:
            assignment = row[subject]
            if pd.notna(assignment) and str(assignment).strip() != '':
                # 获取该科目的时间
                subject_time = df_subjects[df_subjects['课程名称'] == subject]
                if len(subject_time) > 0:
                    time_info = f"{subject_time.iloc[0]['开始时间']} - {subject_time.iloc[0]['结束时间']}"
                    subject_assignments.append((subject, assignment, time_info))
                    print(f"   {subject}({assignment}): {time_info}")

# 检查15考场生物的实际分配
print(f"\n4. 15考场生物的分配情况:")
room15_bio = df_room_result[df_room_result['考场'] == '15考场']
if len(room15_bio) > 0:
    bio_teachers = room15_bio.iloc[0]['生物']
    print(f"   分配的教师: {bio_teachers}")

# 分析可能的冲突
print(f"\n5. 可能的冲突原因分析:")
print("   丁继顺被要求:")
print("   - 必须监考生物科目")
print("   - 必须在15考场监考")
print("   但实际上:")
print("   - 他没有被分配到生物科目")
print("   - 他没有被分配到15考场")
print("   - 15考场的生物被分配给了其他教师")

print(f"\n6. 可能的原因:")
print("   a) 算法优先满足硬约束（场次限制、时间冲突）")
print("   b) 遗传算法变异过程中打乱了必监考约束")
print("   c) 适应度函数中软约束权重不够高")
print("   d) 局部搜索过程中为了整体优化牺牲了个别约束")

# 检查是否有时间冲突
print(f"\n7. 时间冲突检查:")
if len(ding_result) > 0:
    # 获取丁继顺的所有安排时间
    assigned_times = []
    for subject, assignment, time_info in subject_assignments:
        assigned_times.append((subject, time_info))
    
    # 检查生物时间是否与已安排时间冲突
    if len(biology_time) > 0:
        bio_start = biology_time.iloc[0]['开始时间']
        bio_end = biology_time.iloc[0]['结束时间']
        print(f"   生物时间: {bio_start} - {bio_end}")
        
        has_conflict = False
        for subject, time_str in assigned_times:
            # 简单检查是否在同一天
            if "2025-04-29" in str(bio_start) and "2025-04-29" in time_str:
                print(f"   可能与{subject}时间冲突: {time_str}")
                has_conflict = True
        
        if not has_conflict:
            print("   生物时间与现有安排无明显冲突") 