<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}均程通用监考安排{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <!-- Google Fonts -->
    <link href="{{ url_for('static', filename='css/google-fonts.css') }}" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1976D2;
            --primary-light: #64B5F6;
            --accent-color: #FF5722;
            --background-color: #F5F7FA;
            --text-color: #333333;
            --text-secondary: #757575;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
        }

        body {
            padding-top: 70px;
            padding-bottom: 40px;
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            transition: background-color var(--transition-speed);
        }

        .navbar {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .card {
            margin-bottom: 20px;
            border-radius: 8px;
            border: none;
            box-shadow: var(--card-shadow);
            transition: transform 0.2s, box-shadow 0.2s;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            border-bottom: none;
            font-weight: 500;
        }

        .btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #1565C0;
            border-color: #1565C0;
            transform: translateY(-2px);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .footer {
            margin-top: 30px;
            padding: 20px 0;
            color: var(--text-secondary);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            background-color: white;
        }

        .alert {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Animation for page transitions */
        .fade-in {
            animation: fadeIn 0.5s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding-top: 60px;
            }
            .card:hover {
                transform: none;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background-color: #121212;
                --text-color: #E0E0E0;
                --text-secondary: #AAAAAA;
                --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            }

            .card, .footer {
                background-color: #1E1E1E;
            }

            .bg-light {
                background-color: #1E1E1E !important;
            }

            .navbar {
                background-color: rgba(30, 30, 30, 0.8) !important;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body class="fade-in">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background-color: var(--primary-color);">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-calendar-check me-2"></i>均程通用监考安排
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i> 首页
                        </a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i> 控制台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('template_guide') }}">
                            <i class="fas fa-book me-1"></i> 模板说明
                        </a>
                    </li>
                    <li class="nav-item">
                        {% if current_user.role != 'admin' and current_user.tasks.count() >= current_user.task_limit %}
                        <a class="nav-link disabled" href="#"
                           data-bs-toggle="tooltip"
                           data-bs-placement="bottom"
                           title="您已达到任务数量限制，请删除已完成或失败的任务后再创建新任务">
                            <i class="fas fa-plus-circle me-1"></i> 新建任务
                        </a>
                        {% else %}
                        <a class="nav-link" href="{{ url_for('new_task') }}">
                            <i class="fas fa-plus-circle me-1"></i> 新建任务
                        </a>
                        {% endif %}
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i> 控制台
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                                <i class="fas fa-user-circle me-2"></i> 个人信息
                            </a></li>
                            {% if current_user.role == 'admin' %}
                            <li><a class="dropdown-item" href="{{ url_for('admin_dashboard') }}">
                                <i class="fas fa-cog me-2"></i> 管理后台
                            </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i> 登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light btn-sm ms-2 px-3" href="{{ url_for('register') }}">
                            <i class="fas fa-user-plus me-1"></i> 注册
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        <div class="row">
            <div class="col-md-12">
                {% for category, message in messages %}
                <div class="alert alert-{{ category if category != '_' else 'info' }} alert-dismissible fade show shadow-sm" role="alert">
                    <i class="fas {% if category == 'success' %}fa-check-circle{% elif category == 'danger' %}fa-exclamation-circle{% elif category == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer mt-auto py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <h5 class="mb-3"><i class="fas fa-calendar-check me-2"></i>均程通用监考安排</h5>
                    <p class="small">高效、公平的监考任务分配解决方案</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p class="mb-1">均程通用监考安排 &copy; {{ now.year }}</p>
                    <p class="small mb-0">版本 20250420</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>

    <script>
        // Add active class to current nav item
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                    link.setAttribute('aria-current', 'page');
                }
            });

            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // 自动为所有AJAX请求添加CSRF令牌
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const csrfValue = csrfToken.getAttribute('content');

                // 为jQuery AJAX请求添加CSRF令牌
                if (typeof $ !== 'undefined') {
                    $.ajaxSetup({
                        beforeSend: function(xhr, settings) {
                            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                                xhr.setRequestHeader("X-CSRFToken", csrfValue);
                            }
                        }
                    });
                }

                // 为Fetch API添加CSRF令牌
                const originalFetch = window.fetch;
                window.fetch = function(url, options) {
                    options = options || {};
                    if (!options.headers) {
                        options.headers = {};
                    }

                    if (options.method && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(options.method) && !url.includes('http')) {
                        options.headers['X-CSRFToken'] = csrfValue;
                    }

                    return originalFetch(url, options);
                };
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
