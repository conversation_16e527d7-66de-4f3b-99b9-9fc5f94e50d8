#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 配置变量
DOMAIN="jiankao.her5.com"
IP="**************"
APP_DIR="/home/<USER>/jiankao"
NGINX_CONF="/etc/nginx/conf.d/jiankao.conf"
VENV_DIR="$APP_DIR/venv"
CURRENT_DIR=$(pwd)

# 检查是否为root用户
check_root() {
    if [ "$(id -u)" -ne 0 ]; then
        echo -e "${RED}请使用root用户运行此脚本${NC}"
        exit 1
    fi
}

# 系统部署
deploy_system() {
    echo -e "${GREEN}开始系统部署...${NC}"
    
    # 1. 更新系统并安装基础软件
    echo -e "${YELLOW}1. 更新系统并安装基础软件...${NC}"
    apt update && apt upgrade -y
    
    # 安装Python相关包
    apt install -y python3-venv python3-pip
    
    # 安装Nginx
    echo -e "${YELLOW}安装Nginx...${NC}"
    apt install -y nginx

    # 验证Nginx安装
    if ! command -v nginx &> /dev/null; then
        echo -e "${RED}Nginx安装失败！${NC}"
        exit 1
    fi
    
    # 安装Supervisor
    apt install -y supervisor

    # 2. 创建应用目录
    echo -e "${YELLOW}2. 准备应用目录...${NC}"
    mkdir -p $APP_DIR
    
    # 3. 复制应用文件
    echo -e "${YELLOW}3. 复制应用文件...${NC}"
    cp -r $CURRENT_DIR/* $APP_DIR/
    
    # 4. 配置Python虚拟环境
    echo -e "${YELLOW}4. 配置Python虚拟环境...${NC}"
    cd $APP_DIR
    
    # 检查虚拟环境是否完整
    if [ -d "venv" ] && [ -f "venv/bin/activate" ]; then
        echo -e "${GREEN}使用现有虚拟环境${NC}"
        VENV_DIR="$APP_DIR/venv"
    else
        echo -e "${YELLOW}创建新的虚拟环境${NC}"
        # 如果venv目录存在但不完整，先删除它
        rm -rf venv
        python3 -m venv venv
        VENV_DIR="$APP_DIR/venv"
    fi
    
    # 激活虚拟环境并安装依赖
    source $VENV_DIR/bin/activate
    pip install --upgrade pip
    pip install gunicorn  # 安装gunicorn作为WSGI服务器

    # 如果存在requirements.txt，安装依赖
    if [ -f "requirements.txt" ]; then
        echo -e "${YELLOW}安装Python依赖...${NC}"
        pip install -r requirements.txt
    fi

    # 5. 创建必要的目录
    echo -e "${YELLOW}5. 创建应用目录结构...${NC}"
    mkdir -p $APP_DIR/uploads
    mkdir -p $APP_DIR/uploads/tmp
    mkdir -p $APP_DIR/logs
    mkdir -p $APP_DIR/static
    
    # 6. 设置权限
    echo -e "${YELLOW}6. 设置目录权限...${NC}"
    chown -R www-data:www-data $APP_DIR
    chmod -R 755 $APP_DIR
    
    # 7. 配置Supervisor
    echo -e "${YELLOW}7. 配置Supervisor...${NC}"
    cat > /etc/supervisor/conf.d/jiankao.conf << EOF
[program:jiankao]
directory=$APP_DIR
command=$VENV_DIR/bin/gunicorn -w 4 -b 127.0.0.1:8000 app:app
user=www-data
autostart=true
autorestart=true
stderr_logfile=$APP_DIR/logs/supervisor.err.log
stdout_logfile=$APP_DIR/logs/supervisor.out.log
environment=PATH="$VENV_DIR/bin"
EOF

    # 确保Supervisor配置目录存在
    mkdir -p /etc/supervisor/conf.d/
    
    # 重新加载Supervisor配置
    supervisorctl reread
    supervisorctl update
    
    # 8. 配置Nginx
    echo -e "${YELLOW}8. 配置Nginx...${NC}"
    # 确保nginx配置目录存在
    mkdir -p /etc/nginx/conf.d/
    
    # 备份默认配置
    if [ -f /etc/nginx/sites-enabled/default ]; then
        rm /etc/nginx/sites-enabled/default
    fi
    
    # 创建新配置
    create_nginx_config
    
    # 测试Nginx配置
    echo -e "${YELLOW}测试Nginx配置...${NC}"
    nginx -t
    if [ $? -ne 0 ]; then
        echo -e "${RED}Nginx配置测试失败！${NC}"
        exit 1
    fi
    
    # 9. 启动服务
    echo -e "${YELLOW}9. 启动服务...${NC}"
    
    # 启动Supervisor
    if ! systemctl is-active --quiet supervisor; then
        systemctl start supervisor
    fi
    systemctl enable supervisor
    
    # 启动Nginx
    systemctl enable nginx
    systemctl restart nginx
    
    # 最终验证
    echo -e "${YELLOW}验证服务状态...${NC}"
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}Nginx 运行正常${NC}"
    else
        echo -e "${RED}Nginx 启动失败${NC}"
        echo -e "${YELLOW}查看Nginx状态...${NC}"
        systemctl status nginx
        echo -e "${YELLOW}查看Nginx错误日志...${NC}"
        tail -n 20 /var/log/nginx/error.log
    fi
    
    if systemctl is-active --quiet supervisor; then
        echo -e "${GREEN}Supervisor 运行正常${NC}"
        echo -e "${YELLOW}应用状态：${NC}"
        supervisorctl status jiankao
    else
        echo -e "${RED}Supervisor 启动失败${NC}"
        systemctl status supervisor
    fi
    
    echo -e "${GREEN}系统部署完成！${NC}"
    echo -e "${YELLOW}如果遇到问题，请检查以下日志：${NC}"
    echo "1. Nginx错误日志: /var/log/nginx/error.log"
    echo "2. 应用日志: $APP_DIR/logs/supervisor.err.log"
    echo "3. Supervisor日志: /var/log/supervisor/supervisord.log"
}

# 系统状态检查
check_status() {
    echo -e "${YELLOW}检查系统状态...${NC}"
    
    # 检查Nginx状态
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}Nginx 运行状态: 正常${NC}"
    else
        echo -e "${RED}Nginx 运行状态: 异常${NC}"
    fi
    
    # 检查Supervisor状态
    if systemctl is-active --quiet supervisor; then
        echo -e "${GREEN}Supervisor 运行状态: 正常${NC}"
    else
        echo -e "${RED}Supervisor 运行状态: 异常${NC}"
    fi
    
    # 检查应用状态
    if supervisorctl status jiankao | grep -q "RUNNING"; then
        echo -e "${GREEN}应用运行状态: 正常${NC}"
    else
        echo -e "${RED}应用运行状态: 异常${NC}"
    fi
    
    # 检查SSL证书状态
    echo -e "${YELLOW}SSL证书状态：${NC}"
    if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
        echo -e "${GREEN}SSL证书存在${NC}"
        certbot certificates
    else
        echo -e "${RED}SSL证书不存在${NC}"
    fi
}

# 启动系统
start_system() {
    echo -e "${GREEN}启动系统...${NC}"
    systemctl start nginx
    systemctl start supervisor
    supervisorctl start jiankao
}

# 停止系统
stop_system() {
    echo -e "${YELLOW}停止系统...${NC}"
    supervisorctl stop jiankao
    systemctl stop nginx
}

# 重启系统
restart_system() {
    echo -e "${YELLOW}重启系统...${NC}"
    supervisorctl restart jiankao
    systemctl restart nginx
}

# 查看日志
view_logs() {
    echo -e "${YELLOW}查看系统日志...${NC}"
    echo "1. Nginx 错误日志"
    echo "2. 应用日志"
    echo "3. Supervisor 日志"
    read -p "请选择要查看的日志类型 (1/2/3): " log_choice
    
    case $log_choice in
        1) tail -f /var/log/nginx/error.log ;;
        2) tail -f $APP_DIR/logs/app.log ;;
        3) tail -f $APP_DIR/logs/supervisor.*.log ;;
        *) echo -e "${RED}无效的选择${NC}" ;;
    esac
}

# 主菜单
show_menu() {
    echo -e "${GREEN}监考系统管理脚本${NC}"
    echo "------------------------"
    echo "1. 系统部署"
    echo "2. 系统状态检查"
    echo "3. 启动系统"
    echo "4. 停止系统"
    echo "5. 重启系统"
    echo "6. 查看日志"
    echo "0. 退出"
    echo "------------------------"
}

# 主程序
main() {
    check_root
    
    while true; do
        show_menu
        read -p "请选择操作 (0-6): " choice
        
        case $choice in
            1) deploy_system ;;
            2) check_status ;;
            3) start_system ;;
            4) stop_system ;;
            5) restart_system ;;
            6) view_logs ;;
            0) exit 0 ;;
            *) echo -e "${RED}无效的选择${NC}" ;;
        esac
        
        echo
        read -p "按回车键继续..."
    done
}

# 创建Nginx配置
create_nginx_config() {
    local ssl_available=false
    
    # 检查SSL证书是否存在
    if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ] && [ -f "/etc/letsencrypt/live/$DOMAIN/privkey.pem" ]; then
        ssl_available=true
        echo -e "${GREEN}找到SSL证书，将配置HTTPS${NC}"
    else
        echo -e "${YELLOW}未找到SSL证书，将仅配置HTTP${NC}"
    fi

    # 创建Nginx配置文件
    cat > $NGINX_CONF << EOF
# HTTP服务器
server {
    listen 80;
    server_name $DOMAIN;

    # 日志配置
    access_log /var/log/nginx/jiankao.access.log;
    error_log /var/log/nginx/jiankao.error.log;

    # 如果SSL可用，重定向到HTTPS
    $([ "$ssl_available" = true ] && echo "return 301 https://\$server_name\$request_uri;" || echo "# SSL未配置，使用HTTP")

    $([ "$ssl_available" = false ] && cat << 'HTTPCONFIG'
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias $APP_DIR/static;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    location /uploads {
        alias $APP_DIR/uploads;
        internal;
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options DENY;
    }

    client_max_body_size 10M;
HTTPCONFIG
    )
}

$([ "$ssl_available" = true ] && cat << 'HTTPSCONFIG'
# HTTPS服务器
server {
    listen 443 ssl;
    server_name $DOMAIN;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # SSL优化
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_stapling on;
    ssl_stapling_verify on;
    add_header Strict-Transport-Security "max-age=31536000" always;

    # 日志配置
    access_log /var/log/nginx/jiankao.access.log;
    error_log /var/log/nginx/jiankao.error.log;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias $APP_DIR/static;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    location /uploads {
        alias $APP_DIR/uploads;
        internal;
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options DENY;
    }

    client_max_body_size 10M;
}
HTTPSCONFIG
)
EOF

# 运行主程序
main 