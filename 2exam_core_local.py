# 导入所需的Python库
import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass, field
import logging
import time
from pathlib import Path
import os
from ortools.sat.python import cp_model

# 配置日志记录系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('invigilator_scheduler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

@dataclass
class Teacher:
    """教师类：存储和管理教师的监考相关信息及限制条件"""
    id: int                     # 教师的唯一标识ID
    name: str                   # 教师姓名
    max_sessions: int           # 教师可以监考的最大场次数
    must_subjects: Set[str] = field(default_factory=set)    # 教师必须监考的科目集合
    forbidden_subjects: Set[str] = field(default_factory=set)  # 教师不能监考的科目集合
    must_rooms: Set[str] = field(default_factory=set)       # 教师必须监考的考场集合
    forbidden_rooms: Set[str] = field(default_factory=set)   # 教师不能监考的考场集合
    teaching_subject: str = ""   # 教师所教授的科目
    special_settings_count: int = 0  # 教师特殊设置的数量（用于优先级计算）
    available_subjects: Set[str] = field(default_factory=set)  # 教师可以监考的科目集合
    min_sessions: int = 0        # 教师必须监考的最小场次数
    current_sessions: int = 0    # 教师当前已安排的监考场次数
    workload_weight: float = 1.0  # 教师工作量权重
    assigned_times: List[Tuple[datetime, datetime]] = field(default_factory=list)  # 教师已被安排的时间段列表
    group_id: int = 0            # 监考场次限制分组ID

    @classmethod
    def from_excel_row(cls, row):
        """
        从Excel表格的一行数据创建教师对象

        参数:
            row: Excel表格中的一行数据
        返回:
            Teacher: 创建的教师对象
        """
        def split_with_multiple_delimiters(text):
            """使用多个分隔符分割文本"""
            # 首先将所有分隔符统一替换为单个分隔符
            for delimiter in ['，', ', ', ',', '、', '；', '／', ' ']:
                text = text.replace(delimiter, ',')
            # 然后用统一的分隔符分割
            return text.split(',')

        # 处理必须监考和禁止监考的科目与考场
        # 将字符串转换为集合，并支持多种分隔符
        must_subjects = set(split_with_multiple_delimiters(str(row['必监考科目'])))
        forbidden_subjects = set(split_with_multiple_delimiters(str(row['不监考科目'])))
        must_rooms = set(split_with_multiple_delimiters(str(row['必监考考场'])))
        forbidden_rooms = set(split_with_multiple_delimiters(str(row['不监考考场'])))

        # 清理数据，移除空字符串、空白字符、"0"值和nan值
        def clean_set(data_set):
            """清理集合，移除无效数据"""
            cleaned = set()
            for item in data_set:
                # 移除空白字符
                item = item.replace(' ', '').strip()
                # 过滤无效值：空字符串、"0"、"nan"、"NaN"等
                if item and item != "0" and item.lower() not in ["nan", "none", "null"]:
                    cleaned.add(item)
            return cleaned
        
        must_subjects = clean_set(must_subjects)
        forbidden_subjects = clean_set(forbidden_subjects)
        must_rooms = clean_set(must_rooms)
        forbidden_rooms = clean_set(forbidden_rooms)

        # 获取教师任教科目，如果为空、nan或"0"则设为空字符串
        teaching_subject = ""
        if pd.notna(row['任教科目']):
            subject_str = str(row['任教科目']).strip()
            if subject_str and subject_str != "0" and subject_str.lower() != "nan":
                teaching_subject = subject_str

        # 获取场次限制
        max_sessions = int(row['场次限制'])

        # 创建并返回教师对象
        return cls(
            id=int(row['序号']),
            name=str(row['监考老师']),
            must_subjects=must_subjects,
            forbidden_subjects=forbidden_subjects,
            must_rooms=must_rooms,
            forbidden_rooms=forbidden_rooms,
            teaching_subject=teaching_subject,
            max_sessions=max_sessions,
            min_sessions=max_sessions  # 最小场次等于最大场次(场次限制)
        )

@dataclass
class Subject:
    """科目类：存储考试科目的相关信息"""
    code: str                   # 科目代码
    name: str                   # 科目名称
    start_time: datetime        # 考试开始时间
    end_time: datetime          # 考试结束时间
    total_required_teachers: int = 0  # 该科目需要的总监考教师数
    available_teachers: int = 0       # 可用于监考该科目的教师数
    special_teachers: int = 0         # 有特殊要求的教师数
    overlap_count: int = 0            # 与其他科目时间重叠的数量

    @classmethod
    def from_excel_row(cls, row):
        """
        从Excel表格的一行数据创建科目对象

        参数:
            row: Excel表格中的一行数据
        返回:
            Subject: 创建的科目对象
        """
        return cls(
            code=str(row['课程代码']),
            name=str(row['课程名称']),
            start_time=pd.to_datetime(row['开始时间']),
            end_time=pd.to_datetime(row['结束时间'])
        )

@dataclass
class Room:
    """教室类：存储考场信息和各科目所需的监考教师数"""
    name: str                   # 考场名称
    subject_requirements: Dict[str, int]  # 存储各科目所需教师数量的字典

    @classmethod
    def from_excel_row(cls, row, subject_columns):
        """
        从Excel表格的一行数据创建教室对象

        参数:
            row: Excel表格中的一行数据
            subject_columns: 科目列名列表
        返回:
            Room: 创建的教室对象
        """
        # 创建科目需求字典：只包含需要监考教师的科目
        requirements = {}
        for subject in subject_columns:
            if pd.notna(row[subject]) and int(row[subject]) > 0:
                requirements[subject] = int(row[subject])
        return cls(
            name=str(row['考场']),
            subject_requirements=requirements
        )

class SolutionCallback(cp_model.CpSolverSolutionCallback):
    """用于跟踪求解过程中的中间解"""
    def __init__(self, scheduler):
        cp_model.CpSolverSolutionCallback.__init__(self)
        self.scheduler = scheduler
        self.solution_count = 0

    def on_solution_callback(self):
        """每找到一个解就会调用此方法"""
        self.solution_count += 1
        if self.solution_count % 10 == 0:  # 每10个解记录一次日志
            logging.info(f"已找到 {self.solution_count} 个解")

class ORToolsScheduler:
    """使用OR-Tools进行监考排程的调度器"""
    def __init__(self, teachers, subjects, rooms, max_duration_diff=0.5):
        self.teachers = teachers
        self.subjects = subjects
        self.rooms = rooms
        self.subject_map = {subject.name: subject for subject in subjects}
        self.model = cp_model.CpModel()
        self.assignments = {}  # 存储变量字典
        self.subject_room_teacher_vars = {}  # (subject,room,teacher) -> 变量
        self.teacher_assignment_vars = {}  # teacher -> 变量列表
        self.solver = None
        self.solution = None
        self.max_duration_diff = max_duration_diff  # 最大允许监考时长差异（小时）
        self.duration_balance_weight = 1000  # 监考时长均衡的权重系数，从500提高到1000
        self.group_duration_data = []  # 存储组时长分布数据
        self.total_abs_deviation = 0  # 总绝对偏差
        self.total_teachers = 0  # 总教师数

        # 按照场次限制对教师进行分组
        self._group_teachers_by_max_sessions()

    def _group_teachers_by_max_sessions(self):
        """按照最大监考场次对教师进行分组"""
        # 初始化分组字典
        session_groups = {}

        # 为每位教师分配组ID
        group_id = 1
        for teacher in self.teachers:
            if teacher.max_sessions not in session_groups:
                session_groups[teacher.max_sessions] = group_id
                group_id += 1
            teacher.group_id = session_groups[teacher.max_sessions]

        logging.info(f"已将教师按监考场次限制分为 {len(session_groups)} 个组")

    def create_variables(self):
        """创建决策变量"""
        logging.info("正在创建决策变量...")

        # 创建是否分配变量: assignment[subject, room, teacher] = 0/1
        for subject in self.subjects:
            for room in self.rooms:
                if subject.name in room.subject_requirements:
                    for teacher in self.teachers:
                        # 只排除必监考科目的约束，不再排除必监考考场和不监考考场
                        if subject.name not in teacher.forbidden_subjects:
                            var = self.model.NewBoolVar(f'assign_{subject.name}_{room.name}_{teacher.name}')
                            self.assignments[(subject.name, room.name, teacher.name)] = var

                            # 更新教师分配变量列表
                            if teacher.name not in self.teacher_assignment_vars:
                                self.teacher_assignment_vars[teacher.name] = []
                            self.teacher_assignment_vars[teacher.name].append(var)

                            # 更新科目-考场-教师变量字典
                            key = (subject.name, room.name)
                            if key not in self.subject_room_teacher_vars:
                                self.subject_room_teacher_vars[key] = []
                            self.subject_room_teacher_vars[key].append((teacher.name, var))

        logging.info(f"已创建 {len(self.assignments)} 个决策变量")

    def add_constraints(self):
        """添加约束条件"""
        logging.info("正在添加约束条件...")

        # 1. 每个考场的每个科目尽量满足需要的监考教师数量（软约束）
        self.missing_teacher_vars = {}  # 存储缺少的监考教师变量
        for room in self.rooms:
            for subject_name, required_count in room.subject_requirements.items():
                if (subject_name, room.name) in self.subject_room_teacher_vars:
                    teacher_vars = [var for _, var in self.subject_room_teacher_vars[(subject_name, room.name)]]
                    if teacher_vars:
                        # 创建一个变量表示缺少的监考教师数量
                        missing_var = self.model.NewIntVar(0, required_count, f'missing_{subject_name}_{room.name}')
                        self.missing_teacher_vars[(subject_name, room.name)] = missing_var

                        # 添加约束：分配的教师数 + 缺少的教师数 = 需要的教师数
                        self.model.Add(sum(teacher_vars) + missing_var == required_count)

        # 2. 同一时间一个教师只能监考一个考场(即使是相同科目)
        for teacher in self.teachers:
            for subject in self.subjects:
                # 获取该教师在此科目的所有可能考场分配
                room_vars = []
                for room in self.rooms:
                    key = (subject.name, room.name, teacher.name)
                    if key in self.assignments:
                        room_vars.append(self.assignments[key])

                # 如果有多个考场选择，添加约束确保至多选择一个
                if len(room_vars) > 1:
                    self.model.Add(sum(room_vars) <= 1)

        # 3. 必监考要求约束（科目和考场联合约束）
        # 创建变量存储必监考科目和必监考考场的满足情况
        self.must_subject_vars = {}  # 存储必监考科目的满足情况
        self.must_room_vars = {}  # 存储必监考考场的满足情况
        self.forbidden_room_vars = {}  # 存储不监考考场的违反情况

        for teacher in self.teachers:
            # 获取教师的所有必监考要求
            must_subjects = teacher.must_subjects
            must_rooms = teacher.must_rooms
            forbidden_rooms = teacher.forbidden_rooms

            # 3.1 处理必监考科目要求（保持为硬约束）
            for must_subject in must_subjects:
                if must_subject:  # 确保不为空
                    subject_vars = []
                    # 收集所有可能的分配变量
                    for room in self.rooms:
                        key = (must_subject, room.name, teacher.name)
                        if key in self.assignments:
                            subject_vars.append(self.assignments[key])

                    # 添加约束：必监考科目至少安排一次（硬约束）
                    if subject_vars:
                        self.model.Add(sum(subject_vars) >= 1)

                        # 创建变量表示是否在必监考考场监考必监考科目
                        if must_rooms:
                            must_room_subject_vars = []
                            for must_room in must_rooms:
                                if must_room:
                                    key = (must_subject, must_room, teacher.name)
                                    if key in self.assignments:
                                        must_room_subject_vars.append(self.assignments[key])

                            if must_room_subject_vars:
                                # 创建变量表示是否在必监考考场监考必监考科目
                                var_key = (teacher.name, must_subject, "must_room")
                                must_room_var = self.model.NewBoolVar(f'must_room_subject_{teacher.name}_{must_subject}')
                                self.must_subject_vars[var_key] = must_room_var

                                # 添加约束：如果在必监考考场监考必监考科目，则must_room_var为1
                                self.model.Add(sum(must_room_subject_vars) >= 1).OnlyEnforceIf(must_room_var)
                                self.model.Add(sum(must_room_subject_vars) == 0).OnlyEnforceIf(must_room_var.Not())

            # 3.2 处理必监考考场要求（改为软约束）
            if must_rooms:
                for must_room in must_rooms:
                    if must_room:  # 确保不为空
                        # 收集该教师在该考场的所有分配变量
                        room_vars = []
                        for subject in self.subjects:
                            key = (subject.name, must_room, teacher.name)
                            if key in self.assignments:
                                room_vars.append(self.assignments[key])

                        if room_vars:
                            # 创建变量表示是否在必监考考场监考至少一次
                            var_key = (teacher.name, must_room)
                            must_room_var = self.model.NewBoolVar(f'must_room_{teacher.name}_{must_room}')
                            self.must_room_vars[var_key] = must_room_var

                            # 添加约束：如果在必监考考场监考至少一次，则must_room_var为1
                            self.model.Add(sum(room_vars) >= 1).OnlyEnforceIf(must_room_var)
                            self.model.Add(sum(room_vars) == 0).OnlyEnforceIf(must_room_var.Not())

            # 3.3 处理不监考考场要求（改为软约束）
            if forbidden_rooms:
                for forbidden_room in forbidden_rooms:
                    if forbidden_room:  # 确保不为空
                        # 收集该教师在该考场的所有分配变量
                        room_vars = []
                        for subject in self.subjects:
                            key = (subject.name, forbidden_room, teacher.name)
                            if key in self.assignments:
                                room_vars.append(self.assignments[key])

                        if room_vars:
                            # 创建变量表示是否违反不监考考场要求
                            var_key = (teacher.name, forbidden_room)
                            forbidden_room_var = self.model.NewBoolVar(f'forbidden_room_{teacher.name}_{forbidden_room}')
                            self.forbidden_room_vars[var_key] = forbidden_room_var

                            # 添加约束：如果在不监考考场监考至少一次，则forbidden_room_var为1
                            self.model.Add(sum(room_vars) >= 1).OnlyEnforceIf(forbidden_room_var)
                            self.model.Add(sum(room_vars) == 0).OnlyEnforceIf(forbidden_room_var.Not())

        # 4. 教师监考场次限制 - 每位教师尽量监考与其场次限制相等的场次（软约束）
        # 创建变量存储每位教师实际监考的场次数
        self.teacher_session_vars = {}
        self.teacher_session_diff_vars = {}  # 存储与目标场次的差异
        self.teacher_diff_indicators = {}  # 存储差异指示变量

        # 计算所有教师的平均预设场次
        total_max_sessions = sum(teacher.max_sessions for teacher in self.teachers)
        avg_max_sessions = total_max_sessions / len(self.teachers) if self.teachers else 0
        logging.info(f"教师平均预设场次: {avg_max_sessions:.2f}")

        # 创建总体场次差异变量
        max_total_diff = sum(teacher.max_sessions for teacher in self.teachers)  # 最大可能的总差异
        self.total_session_diff_var = self.model.NewIntVar(0, max_total_diff, 'total_session_diff')
        total_diff_terms = []

        for teacher in self.teachers:
            if teacher.name in self.teacher_assignment_vars:
                teacher_vars = self.teacher_assignment_vars[teacher.name]
                
                # 计算教师的最小必需场次（基于必监考科目）
                min_required_sessions = len(teacher.must_subjects) if teacher.must_subjects else 0
                
                # 调整最大场次限制：如果必监考科目数量超过原始场次限制，则放宽限制
                adjusted_max_sessions = max(teacher.max_sessions, min_required_sessions + 1)
                max_possible_diff = max(len(teacher_vars), adjusted_max_sessions)

                # 创建变量表示教师实际监考的场次数
                session_var = self.model.NewIntVar(0, len(teacher_vars), f'sessions_{teacher.name}')
                self.teacher_session_vars[teacher.name] = session_var

                # 添加约束：实际监考场次等于分配变量之和
                self.model.Add(session_var == sum(teacher_vars))

                # 创建变量表示与目标场次的差异（绝对值）
                diff_var = self.model.NewIntVar(0, max_possible_diff, f'session_diff_{teacher.name}')
                self.teacher_session_diff_vars[teacher.name] = diff_var

                # 添加约束：差异变量等于|实际场次 - 目标场次|
                # 由于OR-Tools不直接支持绝对值，我们使用两个约束来实现
                self.model.Add(diff_var >= session_var - teacher.max_sessions)
                self.model.Add(diff_var >= teacher.max_sessions - session_var)

                # 重要修改：将场次限制改为软约束，为必监考科目让路
                # 如果教师有必监考科目，允许超出原始场次限制
                if teacher.must_subjects:
                    # 允许为了满足必监考科目而适当超出场次限制
                    flexible_max = adjusted_max_sessions
                    logging.info(f"教师 {teacher.name} 有必监考科目，场次限制从 {teacher.max_sessions} 调整为 {flexible_max}")
                else:
                    # 没有必监考科目的教师保持原始场次限制
                    flexible_max = teacher.max_sessions
                
                # 添加软约束：尽量不超过调整后的最大场次限制
                self.model.Add(session_var <= flexible_max)

                # 为分层惩罚创建指示变量
                self.teacher_diff_indicators[teacher.name] = {}

                # 对差异为0、1、2及以上的情况分别创建指示变量
                for i in range(min(3, max_possible_diff + 1)):
                    indicator = self.model.NewBoolVar(f'diff_{teacher.name}_is_{i}')
                    if i < 2:  # 对于差异0和1，精确匹配
                        self.model.Add(diff_var == i).OnlyEnforceIf(indicator)
                        self.model.Add(diff_var != i).OnlyEnforceIf(indicator.Not())
                    else:  # 对于差异2及以上，使用大于等于
                        self.model.Add(diff_var >= i).OnlyEnforceIf(indicator)
                        self.model.Add(diff_var < i).OnlyEnforceIf(indicator.Not())
                    self.teacher_diff_indicators[teacher.name][i] = indicator

                # 将此教师的差异添加到总体差异中
                total_diff_terms.append(diff_var)

        # 添加总体场次差异约束
        if total_diff_terms:
            self.model.Add(self.total_session_diff_var == sum(total_diff_terms))

        # 5. 教师监考时长约束（同一组内的教师监考时长差异不超过阈值）
        self._add_duration_constraints()

        # 6. 优化目标: 定义目标函数
        self._define_objective_function()

        logging.info("已添加所有约束条件")

    def _add_duration_constraints(self):
        """添加教师监考时长约束"""
        logging.info("正在添加教师监考时长约束...")

        # 为每位教师创建监考时长变量
        self.teacher_duration_vars = {}
        for teacher in self.teachers:
            # 创建代表教师总监考时长的变量（单位：分钟）
            duration_var = self.model.NewIntVar(0, 1000, f'duration_{teacher.name}')
            self.teacher_duration_vars[teacher.name] = duration_var

            # 计算教师的总监考时长
            duration_terms = []
            for subject in self.subjects:
                subject_duration = int((subject.end_time - subject.start_time).total_seconds() / 60)  # 转换为分钟
                for room in self.rooms:
                    key = (subject.name, room.name, teacher.name)
                    if key in self.assignments:
                        var = self.assignments[key]
                        duration_terms.append(subject_duration * var)

            if duration_terms:
                self.model.Add(duration_var == sum(duration_terms))

        # 按照组ID对教师进行分组
        self.teacher_groups = {}
        for teacher in self.teachers:
            if teacher.group_id not in self.teacher_groups:
                self.teacher_groups[teacher.group_id] = []
            self.teacher_groups[teacher.group_id].append(teacher)

        # 为每个组创建时长变量，但不添加硬约束
        self.group_max_duration = {}
        self.group_min_duration = {}
        self.group_diff_duration = {}

        for group_id, group_teachers in self.teacher_groups.items():
            if len(group_teachers) <= 1:
                continue  # 跳过只有一位教师的组

            # 为每个组创建最大和最小监考时长变量
            group_max = self.model.NewIntVar(0, 1000, f'group_{group_id}_max_duration')
            group_min = self.model.NewIntVar(0, 1000, f'group_{group_id}_min_duration')
            self.group_max_duration[group_id] = group_max
            self.group_min_duration[group_id] = group_min

            # 设置组内最大和最小监考时长
            for teacher in group_teachers:
                teacher_duration = self.teacher_duration_vars[teacher.name]
                self.model.Add(teacher_duration <= group_max)
                self.model.Add(teacher_duration >= group_min)

            # 创建差异变量，但不添加硬约束（将在目标函数中使用）
            group_diff = self.model.NewIntVar(0, 1000, f'group_{group_id}_diff_duration')
            self.model.Add(group_diff == group_max - group_min)
            self.group_diff_duration[group_id] = group_diff

        logging.info("已添加监考时长变量和关系约束")

    def _define_objective_function(self):
        """定义目标函数"""
        objective_terms = []
        obj_coef = {}  # 目标函数系数

        # 0. 必监考科目最高优先级（新增）
        # 由于必监考科目已经是硬约束，这里添加额外的高权重奖励以确保优先满足
        must_subject_priority_weight = 50000  # 最高权重
        for teacher in self.teachers:
            for must_subject in teacher.must_subjects:
                if must_subject:
                    for room in self.rooms:
                        var_key = (must_subject, room.name, teacher.name)
                        if var_key in self.assignments:
                            var = self.assignments[var_key]
                            obj_coef[var] = obj_coef.get(var, 0) + must_subject_priority_weight

        # 1. 优先考虑教师教授的科目
        for teacher in self.teachers:
            if teacher.teaching_subject:
                for room in self.rooms:
                    var_key = (teacher.teaching_subject, room.name, teacher.name)
                    if var_key in self.assignments:
                        var = self.assignments[var_key]
                        obj_coef[var] = obj_coef.get(var, 0) + 10  # 增加权重

        # 2. 添加监考时长均衡奖惩项
        # 将监考时长差异作为软约束，给予奖励或惩罚
        for group_id, diff_var in self.group_diff_duration.items():
            # 创建评估变量，判断是否满足时长差异要求
            max_diff_minutes = int(self.max_duration_diff * 60)  # 将小时转为分钟

            # 创建一个布尔变量，表示是否满足时长差异要求
            is_within_limit = self.model.NewBoolVar(f'group_{group_id}_within_limit')

            # 如果差异小于等于阈值，is_within_limit为1；否则为0
            self.model.Add(diff_var <= max_diff_minutes).OnlyEnforceIf(is_within_limit)
            self.model.Add(diff_var > max_diff_minutes).OnlyEnforceIf(is_within_limit.Not())

            # 添加奖励项：如果满足时长差异要求，给予奖励；否则给予惩罚
            # 奖励项：is_within_limit * reward_weight
            # 惩罚项：-diff_var * (1-is_within_limit) * penalty_weight

            # 添加奖励项
            objective_terms.append(self.duration_balance_weight * is_within_limit)

            # 添加惩罚项（使用线性化技巧）
            # 创建惩罚变量，表示超出阈值的差异部分
            penalty_var = self.model.NewIntVar(0, 1000, f'group_{group_id}_penalty')

            # 只有当超出阈值时，计算惩罚值
            # penalty_var = (diff_var - max_diff_minutes) * (1 - is_within_limit)
            self.model.Add(penalty_var >= diff_var - max_diff_minutes).OnlyEnforceIf(is_within_limit.Not())
            self.model.Add(penalty_var == 0).OnlyEnforceIf(is_within_limit)

            # 将惩罚变量加入目标函数（负权重）- 增加对超出阈值的惩罚力度
            objective_terms.append(-10 * self.duration_balance_weight * penalty_var)  # 从5倍提高到10倍

        # 3. 软约束：同一考场监考人数大于2人时，监考老师教授的学科尽量不同
        # 为每个可能的考场和科目组合添加奖励项
        diverse_teaching_weight = 50  # 教授科目多样性的权重

        for subject in self.subjects:
            for room in self.rooms:
                if subject.name in room.subject_requirements and room.subject_requirements[subject.name] >= 2:
                    # 获取所有可能分配到此考场此科目的教师
                    teachers_by_subject = {}
                    for teacher in self.teachers:
                        if teacher.teaching_subject:  # 只考虑有教授科目的教师
                            key = (subject.name, room.name, teacher.name)
                            if key in self.assignments:
                                if teacher.teaching_subject not in teachers_by_subject:
                                    teachers_by_subject[teacher.teaching_subject] = []
                                teachers_by_subject[teacher.teaching_subject].append(self.assignments[key])

                    # 对于每种教授科目，奖励至多选择一位教师
                    for teaching_subject, teacher_vars in teachers_by_subject.items():
                        if len(teacher_vars) > 1:
                            # 创建辅助变量，表示是否至多选择一位该科目的教师
                            at_most_one = self.model.NewBoolVar(f'at_most_one_{subject.name}_{room.name}_{teaching_subject}')

                            # 如果该教授科目的教师选择不超过一个，at_most_one为1
                            self.model.Add(sum(teacher_vars) <= 1).OnlyEnforceIf(at_most_one)
                            self.model.Add(sum(teacher_vars) > 1).OnlyEnforceIf(at_most_one.Not())

                            # 添加奖励项
                            objective_terms.append(diverse_teaching_weight * at_most_one)

        # 4. 添加对缺少监考教师的惩罚（最高权重）
        missing_teacher_weight = 10000  # 缺少监考教师的惩罚权重（程序中最高权重）
        for (subject_name, room), missing_var in self.missing_teacher_vars.items():
            # 添加惩罚项：每缺少一位监考教师，扣除一定分数
            objective_terms.append(-missing_teacher_weight * missing_var)

        # 5. 添加对必监考考场和不监考考场的奖励和惩罚
        must_room_weight = 9000  # 必监考考场的奖励权重（高于场次差异但低于缺少监考教师）
        forbidden_room_penalty = 9000  # 不监考考场的惩罚权重
        must_subject_room_weight = 9500  # 在必监考考场监考必监考科目的奖励权重

        # 5.1 添加对在必监考考场监考必监考科目的奖励
        for var_key, var in self.must_subject_vars.items():
            objective_terms.append(must_subject_room_weight * var)

        # 5.2 添加对在必监考考场监考的奖励
        for var_key, var in self.must_room_vars.items():
            objective_terms.append(must_room_weight * var)

        # 5.3 添加对在不监考考场监考的惩罚
        for var_key, var in self.forbidden_room_vars.items():
            objective_terms.append(-forbidden_room_penalty * var)

        # 6. 添加对教师场次差异的分层惩罚（最高权重）
        # 设置不同差异级别的惩罚权重
        no_diff_reward = 5000  # 无差异的奖励
        small_diff_penalty = 8000  # 差异为1的惩罚
        large_diff_penalty = 12000  # 差异为2及以上的惩罚

        # 对每位教师应用分层惩罚
        for teacher_name, indicators in self.teacher_diff_indicators.items():
            # 差异为0时给予奖励
            if 0 in indicators:
                objective_terms.append(no_diff_reward * indicators[0])

            # 差异为1时施加中等惩罚
            if 1 in indicators:
                objective_terms.append(-small_diff_penalty * indicators[1])

            # 差异为2及以上时施加更高惩罚
            if 2 in indicators:
                # 获取实际差异值，用于非线性惩罚
                diff_var = self.teacher_session_diff_vars[teacher_name]
                # 创建惩罚变量，表示超出1的差异部分
                excess_diff_var = self.model.NewIntVar(0, 10, f'excess_diff_{teacher_name}')
                self.model.Add(excess_diff_var == diff_var - 1).OnlyEnforceIf(indicators[2])
                self.model.Add(excess_diff_var == 0).OnlyEnforceIf(indicators[2].Not())

                # 添加非线性惩罚：基础惩罚 + 额外惩罚 * (差异-1)
                objective_terms.append(-large_diff_penalty * indicators[2])
                objective_terms.append(-large_diff_penalty * excess_diff_var)

        # 6. 添加对总体场次差异的惩罚
        total_diff_penalty_weight = 5000  # 总体差异惩罚权重
        objective_terms.append(-total_diff_penalty_weight * self.total_session_diff_var)

        # 7. 添加对超出平均场次过多的教师的额外惩罚
        # 这部分可以在未来版本中实现

        # 构建目标函数
        for var, coef in obj_coef.items():
            objective_terms.append(coef * var)

        # 设置优化目标
        self.model.Maximize(sum(objective_terms))

    def solve(self, time_limit_seconds=300):
        """求解模型"""
        logging.info(f"开始求解模型，时间限制：{time_limit_seconds}秒...")

        # 添加诊断分析
        self._diagnose_constraints()

        start_time = time.time()

        # 设置求解器参数
        self.solver = cp_model.CpSolver()
        self.solver.parameters.max_time_in_seconds = time_limit_seconds
        self.solver.parameters.num_search_workers = os.cpu_count()
        self.solver.parameters.log_search_progress = True

        # 启用中间解
        solution_callback = SolutionCallback(self)

        # 求解模型
        status = self.solver.Solve(self.model, solution_callback)

        solve_time = time.time() - start_time

        # 处理求解结果
        if status == cp_model.OPTIMAL:
            logging.info(f"找到最优解！求解时间：{solve_time:.2f}秒")
            self.solution = self._extract_solution()
            return self.solution
        elif status == cp_model.FEASIBLE:
            logging.info(f"找到可行解（非最优）！求解时间：{solve_time:.2f}秒")
            self.solution = self._extract_solution()
            return self.solution
        elif solution_callback.solution_count > 0:
            # 使用最后一个中间解
            logging.warning(f"求解器未返回最终解，但找到了{solution_callback.solution_count}个中间解。使用最后一个中间解。")
            self.solution = self._extract_solution()
            return self.solution
        else:
            logging.error(f"未找到任何可行解。状态码：{status}，求解时间：{solve_time:.2f}秒")
            
            # 尝试渐进式求解策略
            logging.info("尝试渐进式求解策略...")
            relaxed_solution = self._progressive_solve()
            if relaxed_solution:
                return relaxed_solution
            
            # 创建一个空的解决方案，以便程序能够继续运行
            self.solution = Schedule()
            return self.solution

    def _extract_solution(self):
        """从求解结果中提取解决方案"""
        schedule = Schedule()

        # 从决策变量提取分配结果
        for (subject, room, teacher), var in self.assignments.items():
            if self.solver.Value(var) == 1:
                schedule.add_assignment(subject, room, teacher)

        # 提取缺少的监考教师信息
        if hasattr(self, 'missing_teacher_vars'):
            for (subject_name, room_name), missing_var in self.missing_teacher_vars.items():
                missing_count = self.solver.Value(missing_var)
                if missing_count > 0:
                    # 将缺少的监考教师信息添加到调度方案中
                    schedule.add_missing_teachers(subject_name, room_name, missing_count)

        # 提取教师场次差异信息
        if hasattr(self, 'teacher_session_vars'):
            # 记录总体场次差异
            if hasattr(self, 'total_session_diff_var'):
                schedule.total_session_diff = self.solver.Value(self.total_session_diff_var)
                logging.info(f"总体场次差异: {schedule.total_session_diff}")

            # 记录每位教师的场次差异
            session_diff_stats = {0: 0, 1: 0, 2: 0, 'more': 0}  # 统计不同差异级别的教师数量

            for teacher_name, session_var in self.teacher_session_vars.items():
                actual_sessions = self.solver.Value(session_var)

                # 找到对应的教师对象
                for teacher in self.teachers:
                    if teacher.name == teacher_name:
                        # 记录实际安排的场次数
                        teacher.current_sessions = actual_sessions

                        # 计算差异
                        diff = abs(actual_sessions - teacher.max_sessions)
                        teacher.session_diff = diff

                        # 统计差异
                        if diff <= 2:
                            session_diff_stats[diff] += 1
                        else:
                            session_diff_stats['more'] += 1

                        break

            # 记录场次差异统计信息
            schedule.session_diff_stats = session_diff_stats
            logging.info(f"场次差异统计: 无差异={session_diff_stats[0]}人, 差异1={session_diff_stats[1]}人, " +
                        f"差异2={session_diff_stats[2]}人, 更大差异={session_diff_stats['more']}人")

        # 提取必监考考场和不监考考场的满足情况
        must_room_stats = {'满足': 0, '不满足': 0}
        forbidden_room_stats = {'满足': 0, '违反': 0}

        # 记录必监考考场的满足情况
        if hasattr(self, 'must_room_vars'):
            for var_key, var in self.must_room_vars.items():
                if self.solver.Value(var) == 1:
                    must_room_stats['满足'] += 1
                    # 记录到教师对象
                    teacher_name, room_name = var_key
                    for teacher in self.teachers:
                        if teacher.name == teacher_name:
                            if not hasattr(teacher, 'satisfied_must_rooms'):
                                teacher.satisfied_must_rooms = []
                            teacher.satisfied_must_rooms.append(room_name)
                            break
                else:
                    must_room_stats['不满足'] += 1

        # 记录不监考考场的满足情况
        if hasattr(self, 'forbidden_room_vars'):
            for var_key, var in self.forbidden_room_vars.items():
                if self.solver.Value(var) == 0:
                    forbidden_room_stats['满足'] += 1
                else:
                    forbidden_room_stats['违反'] += 1
                    # 记录到教师对象
                    teacher_name, room_name = var_key
                    for teacher in self.teachers:
                        if teacher.name == teacher_name:
                            if not hasattr(teacher, 'violated_forbidden_rooms'):
                                teacher.violated_forbidden_rooms = []
                            teacher.violated_forbidden_rooms.append(room_name)
                            break

        # 记录到调度方案中
        schedule.must_room_stats = must_room_stats
        schedule.forbidden_room_stats = forbidden_room_stats

        logging.info(f"必监考考场统计: 满足={must_room_stats['满足']}, 不满足={must_room_stats['不满足']}")
        logging.info(f"不监考考场统计: 满足={forbidden_room_stats['满足']}, 违反={forbidden_room_stats['违反']}")

        # 收集每组的监考时长情况
        self.group_duration_data = []
        for group_id, teachers in self.teacher_groups.items():
            if len(teachers) <= 1:
                continue

            teacher_times = []
            for teacher in teachers:
                if teacher.name in self.teacher_duration_vars:
                    duration_var = self.teacher_duration_vars[teacher.name]
                    duration_value = self.solver.Value(duration_var)
                    teacher_times.append((teacher.name, duration_value))

            if teacher_times:
                times = [t[1] for t in teacher_times]
                max_time = max(times)
                min_time = min(times)
                diff = max_time - min_time
                avg_time = sum(times) / len(times)

                # 计算每个教师与平均值的偏差
                abs_deviations = [abs(t - avg_time) for t in times]
                avg_deviation = sum(abs_deviations) / len(abs_deviations)

                # 累计总统计
                self.total_abs_deviation += sum(abs_deviations)
                self.total_teachers += len(times)

                # 添加组数据
                group_data = {
                    '组ID': group_id,
                    '场次限制': teachers[0].max_sessions,
                    '教师人数': len(teacher_times),
                    '平均监考时长(小时)': round(avg_time/60, 2),
                    '最长监考时长(小时)': round(max_time/60, 2),
                    '最短监考时长(小时)': round(min_time/60, 2),
                    '组内差异(小时)': round(diff/60, 2),
                    '平均偏差(小时)': round(avg_deviation/60, 4),
                    '是否超出阈值': '是' if diff > self.max_duration_diff * 60 else '否'
                }

                # 添加教师详情
                for name, time in sorted(teacher_times, key=lambda x: x[1]):
                    deviation = abs(time - avg_time)
                    group_data[f'教师_{name}_时长(小时)'] = round(time/60, 2)
                    group_data[f'教师_{name}_偏差(小时)'] = round(deviation/60, 4)

                self.group_duration_data.append(group_data)

        return schedule

    def export_group_duration_data(self, writer: pd.ExcelWriter):
        """导出组时长分布数据到Excel"""
        if self.group_duration_data:
            # 创建DataFrame
            df = pd.DataFrame(self.group_duration_data)

            # 添加总体统计
            if self.total_teachers > 0:
                global_avg_deviation = self.total_abs_deviation / self.total_teachers
                total_stats = pd.DataFrame([{
                    '组ID': '总体统计',
                    '场次限制': '-',
                    '教师人数': self.total_teachers,
                    '平均监考时长(小时)': '-',
                    '最长监考时长(小时)': '-',
                    '最短监考时长(小时)': '-',
                    '组内差异(小时)': '-',
                    '平均偏差(小时)': round(global_avg_deviation/60, 4),
                    '是否超出阈值': '-'
                }])
                df = pd.concat([df, total_stats], ignore_index=True)

            # 导出到Excel
            df.to_excel(writer, sheet_name='组时长分布', index=False)

    def _is_time_overlap(self, slot1: Tuple[datetime, datetime],
                        slot2: Tuple[datetime, datetime]) -> bool:
        """
        检查两个时间段是否重叠

        参数:
            slot1: 第一个时间段(开始时间,结束时间)
            slot2: 第二个时间段(开始时间,结束时间)
        返回:
            bool: 是否重叠
        """
        start1, end1 = slot1
        start2, end2 = slot2
        return max(start1, start2) < min(end1, end2)

    def _diagnose_constraints(self):
        """诊断约束冲突，分析必监考科目的可行性"""
        logging.info("开始诊断约束冲突...")
        
        # 1. 分析必监考科目需求
        must_subject_demands = {}
        for teacher in self.teachers:
            for must_subject in teacher.must_subjects:
                if must_subject:
                    if must_subject not in must_subject_demands:
                        must_subject_demands[must_subject] = 0
                    must_subject_demands[must_subject] += 1
        
        # 2. 分析每个科目的可用席位
        subject_available_slots = {}
        for subject in self.subjects:
            total_slots = sum(room.subject_requirements.get(subject.name, 0) for room in self.rooms)
            subject_available_slots[subject.name] = total_slots
        
        # 3. 检查必监考科目的资源冲突
        conflicts = []
        for subject, demand in must_subject_demands.items():
            available = subject_available_slots.get(subject, 0)
            if demand > available:
                conflicts.append(f"科目'{subject}': 需要{demand}位教师必监考，但只有{available}个席位")
        
        # 4. 分析教师场次与必监考科目的冲突
        teacher_conflicts = []
        for teacher in self.teachers:
            if teacher.must_subjects:
                # 计算教师必监考科目的最小时长
                min_required_duration = 0
                for must_subject in teacher.must_subjects:
                    if must_subject:
                        subject_obj = None
                        for s in self.subjects:
                            if s.name == must_subject:
                                subject_obj = s
                                break
                        if subject_obj:
                            duration = (subject_obj.end_time - subject_obj.start_time).total_seconds() / 3600
                            min_required_duration += duration
                
                # 计算教师最大可监考时长（基于场次限制）
                max_possible_duration = 0
                for subject in self.subjects:
                    duration = (subject.end_time - subject.start_time).total_seconds() / 3600
                    max_possible_duration = max(max_possible_duration, duration)
                max_total_duration = max_possible_duration * teacher.max_sessions
                
                if min_required_duration > max_total_duration:
                    teacher_conflicts.append(f"教师'{teacher.name}': 必监考科目最小时长{min_required_duration:.2f}小时 > 最大可监考时长{max_total_duration:.2f}小时")
        
        # 5. 输出诊断结果
        if conflicts or teacher_conflicts:
            logging.error("发现约束冲突:")
            for conflict in conflicts:
                logging.error(f"  资源冲突: {conflict}")
            for conflict in teacher_conflicts:
                logging.error(f"  教师冲突: {conflict}")
            
            # 输出解决建议
            logging.info("建议解决方案:")
            if conflicts:
                logging.info("  1. 增加考场席位或减少必监考科目要求")
                logging.info("  2. 调整教师的必监考科目分配")
            if teacher_conflicts:
                logging.info("  3. 增加相关教师的场次限制")
                logging.info("  4. 减少教师的必监考科目数量")
        else:
            logging.info("初步诊断未发现明显的资源冲突")
        
        # 6. 分析教师总场次供需平衡
        total_supply = sum(teacher.max_sessions for teacher in self.teachers)
        total_demand = sum(sum(room.subject_requirements.values()) for room in self.rooms)
        
        logging.info(f"场次供需分析: 总供给={total_supply}, 总需求={total_demand}, 差额={total_supply-total_demand}")
        
        if total_supply < total_demand:
            logging.warning(f"警告: 教师场次总供给不足，缺口{total_demand-total_supply}场次")
        
        logging.info("约束诊断完成")

    def _progressive_solve(self):
        """渐进式求解：逐步放松约束直到找到可行解"""
        logging.info("开始渐进式求解...")
        
        # 阶段1：只保留最基本约束 - 必监考科目和基本分配约束
        logging.info("阶段1：只保留必监考科目和基本分配约束")
        basic_model = cp_model.CpModel()
        basic_assignments = {}
        
        # 重新创建基本变量
        for subject in self.subjects:
            for room in self.rooms:
                if subject.name in room.subject_requirements:
                    for teacher in self.teachers:
                        if subject.name not in teacher.forbidden_subjects:
                            var = basic_model.NewBoolVar(f'assign_{subject.name}_{room.name}_{teacher.name}')
                            basic_assignments[(subject.name, room.name, teacher.name)] = var
        
        # 添加基本约束1：每个考场的监考需求（允许不足）
        missing_vars = {}
        for room in self.rooms:
            for subject_name, required_count in room.subject_requirements.items():
                teacher_vars = []
                for teacher in self.teachers:
                    key = (subject_name, room.name, teacher.name)
                    if key in basic_assignments:
                        teacher_vars.append(basic_assignments[key])
                
                if teacher_vars:
                    # 允许监考人数不足，但记录缺少的数量
                    missing_var = basic_model.NewIntVar(0, required_count, f'missing_{subject_name}_{room.name}')
                    missing_vars[(subject_name, room.name)] = missing_var
                    basic_model.Add(sum(teacher_vars) + missing_var == required_count)
        
        # 添加基本约束2：同一时间一个教师只能监考一个考场
        for teacher in self.teachers:
            for subject in self.subjects:
                room_vars = []
                for room in self.rooms:
                    key = (subject.name, room.name, teacher.name)
                    if key in basic_assignments:
                        room_vars.append(basic_assignments[key])
                if len(room_vars) > 1:
                    basic_model.Add(sum(room_vars) <= 1)
        
        # 添加基本约束3：必监考科目（硬约束）
        for teacher in self.teachers:
            for must_subject in teacher.must_subjects:
                if must_subject:
                    subject_vars = []
                    for room in self.rooms:
                        key = (must_subject, room.name, teacher.name)
                        if key in basic_assignments:
                            subject_vars.append(basic_assignments[key])
                    if subject_vars:
                        basic_model.Add(sum(subject_vars) >= 1)
        
        # 添加基本约束4：场次限制（放宽版本）
        for teacher in self.teachers:
            teacher_vars = []
            for subject in self.subjects:
                for room in self.rooms:
                    key = (subject.name, room.name, teacher.name)
                    if key in basic_assignments:
                        teacher_vars.append(basic_assignments[key])
            
            if teacher_vars:
                # 放宽场次限制：允许最多比原限制多2场
                relaxed_max = teacher.max_sessions + 2
                basic_model.Add(sum(teacher_vars) <= relaxed_max)
        
        # 设置简单目标函数：最小化缺少的监考教师数量
        objective_terms = []
        for missing_var in missing_vars.values():
            objective_terms.append(-1000 * missing_var)  # 惩罚缺少监考教师
        
        # 添加对必监考科目的奖励
        for teacher in self.teachers:
            for must_subject in teacher.must_subjects:
                if must_subject:
                    for room in self.rooms:
                        key = (must_subject, room.name, teacher.name)
                        if key in basic_assignments:
                            objective_terms.append(10000 * basic_assignments[key])
        
        if objective_terms:
            basic_model.Maximize(sum(objective_terms))
        
        # 求解基本模型
        basic_solver = cp_model.CpSolver()
        basic_solver.parameters.max_time_in_seconds = 60  # 1分钟时间限制
        basic_status = basic_solver.Solve(basic_model)
        
        if basic_status in [cp_model.OPTIMAL, cp_model.FEASIBLE]:
            logging.info("阶段1成功：找到满足必监考科目的基本解")
            
            # 提取基本解
            basic_schedule = Schedule()
            for (subject, room, teacher), var in basic_assignments.items():
                if basic_solver.Value(var) == 1:
                    basic_schedule.add_assignment(subject, room, teacher)
            
            # 记录缺少的监考教师
            for (subject_name, room_name), missing_var in missing_vars.items():
                missing_count = basic_solver.Value(missing_var)
                if missing_count > 0:
                    basic_schedule.add_missing_teachers(subject_name, room_name, missing_count)
            
            self.solution = basic_schedule
            return basic_schedule
        else:
            logging.error("阶段1失败：即使在放松约束下也无法满足必监考科目要求")
            logging.error("建议检查数据设置，可能存在不可解决的冲突")
            return None

class Schedule:
    """调度方案类：存储和管理监考安排方案"""
    def __init__(self):
        # 存储监考安排：(科目,考场) -> [教师列表]
        self.assignments: Dict[Tuple[str, str], List[str]] = {}
        # 存储教师的监考安排：教师 -> [(科目,考场)]
        self.teacher_assignments: Dict[str, List[Tuple[str, str]]] = {}
        # 存储缺少的监考教师信息：(科目,考场) -> 缺少的教师数量
        self.missing_teachers: Dict[Tuple[str, str], int] = {}
        # 存储方案的适应度分数
        self.fitness_score: float = 0.0
        # 存储总体场次差异
        self.total_session_diff: int = 0
        # 存储场次差异统计信息
        self.session_diff_stats: Dict = {0: 0, 1: 0, 2: 0, 'more': 0}
        # 存储必监考考场满足情况
        self.must_room_stats: Dict = {'满足': 0, '不满足': 0}
        # 存储不监考考场满足情况
        self.forbidden_room_stats: Dict = {'满足': 0, '违反': 0}

    def add_assignment(self, subject: str, room: str, teacher: str):
        """
        添加一个监考安排

        参数:
            subject: 科目名称
            room: 考场名称
            teacher: 教师名称
        """
        key = (subject, room)
        if key not in self.assignments:
            self.assignments[key] = []
        self.assignments[key].append(teacher)

        if teacher not in self.teacher_assignments:
            self.teacher_assignments[teacher] = []
        self.teacher_assignments[teacher].append(key)

    def add_missing_teachers(self, subject: str, room: str, count: int):
        """
        添加缺少的监考教师信息

        参数:
            subject: 科目名称
            room: 考场名称
            count: 缺少的教师数量
        """
        key = (subject, room)
        self.missing_teachers[key] = count

        # 如果assignments中没有这个键，初始化为空列表
        if key not in self.assignments:
            self.assignments[key] = []

    def calculate_fitness(self, scheduler) -> float:
        """计算适应度分数 (兼容性函数)"""
        self.fitness_score = 1000000  # OR-Tools解决方案应该是最优的
        return self.fitness_score

class ExamScheduler:
    """考试监考安排系统"""
    def __init__(self, excel_file: str, max_duration_diff=0.5):
        """
        初始化考试监考安排系统

        参数:
            excel_file: Excel文件路径
            max_duration_diff: 同组教师间允许的最大监考时长差异（小时）
        """
        self.excel_file = excel_file
        self.teachers: List[Teacher] = []  # 教师列表
        self.subjects: List[Subject] = []  # 科目列表
        self.original_subjects: List[Subject] = []  # 原始顺序的科目列表
        self.rooms: List[Room] = []  # 教室列表
        self.assignments: Dict[Tuple[str, str, str], List[str]] = {}  # 监考安排
        self.best_schedule: Schedule = None  # 最优调度方案
        self.current_schedule: Schedule = None  # 当前调度方案
        self.max_duration_diff = max_duration_diff  # 最大允许监考时长差异（小时）

        # 科目名称到科目对象的映射
        self.subject_map: Dict[str, Subject] = {}

        # 加载数据
        self._load_data()

    def _load_data(self):
        """从Excel文件加载数据"""
        try:
            # 读取Excel文件的三个工作表
            teacher_df = pd.read_excel(self.excel_file, sheet_name='监考员设置')
            subject_df = pd.read_excel(self.excel_file, sheet_name='考试科目设置')
            room_df = pd.read_excel(self.excel_file, sheet_name='考场设置')

            # 处理教师数据
            for _, row in teacher_df.iterrows():
                teacher = Teacher.from_excel_row(row)
                self.teachers.append(teacher)

            # 处理科目数据
            for _, row in subject_df.iterrows():
                subject = Subject.from_excel_row(row)
                self.subjects.append(subject)
                self.original_subjects.append(subject)
                self.subject_map[subject.name] = subject

            # 处理考场数据
            subject_columns = [col for col in room_df.columns if col != '考场']
            for _, row in room_df.iterrows():
                room = Room.from_excel_row(row, subject_columns)
                self.rooms.append(room)

            logging.info(f"成功加载数据: {len(self.teachers)}位教师, "
                        f"{len(self.subjects)}个科目, {len(self.rooms)}个考场")

        except Exception as e:
            logging.error(f"加载数据时出错: {str(e)}")
            raise

    def preprocess_teachers(self):
        """教师信息预处理：计算教师权重并排序"""
        # 获取所有科目集合
        all_subjects = {subject.name for subject in self.subjects}

        for teacher in self.teachers:
            # 设置教师可监考的科目
            teacher.available_subjects = all_subjects - teacher.forbidden_subjects
            if teacher.must_subjects:
                teacher.available_subjects.update(teacher.must_subjects)

        logging.info("完成教师信息预处理")

    def preprocess_subjects(self):
        """科目安排顺序预处理：计算科目优先级"""
        for subject in self.subjects:
            # 计算每个科目需要的总监考人数
            total_required = sum(
                room.subject_requirements.get(subject.name, 0)
                for room in self.rooms
            )
            subject.total_required_teachers = total_required

            # 计算可用教师数
            available_teachers = sum(
                1 for teacher in self.teachers
                if subject.name in teacher.available_subjects
            )
            subject.available_teachers = available_teachers

            # 计算特殊要求教师数
            subject.special_teachers = sum(
                1 for teacher in self.teachers
                if subject.name in teacher.must_subjects
            )

            # 计算与其他科目的时间重叠数
            subject.overlap_count = sum(
                1 for other in self.subjects
                if other != subject and self._is_time_overlap(
                    (subject.start_time, subject.end_time),
                    (other.start_time, other.end_time)
                )
            )

        logging.info("完成科目安排顺序预处理")

    def schedule_exams(self) -> Schedule:
        """
        使用OR-Tools执行监考安排算法

        返回:
            Schedule: 最优的监考安排方案
        """
        logging.info("开始使用OR-Tools进行监考安排...")

        # 创建OR-Tools求解器
        self.scheduler = ORToolsScheduler(self.teachers, self.subjects, self.rooms, self.max_duration_diff)

        # 创建决策变量
        self.scheduler.create_variables()

        # 添加约束条件
        self.scheduler.add_constraints()

        # 求解模型 (默认5分钟时间限制)
        solution = self.scheduler.solve(300)

        # 无论是否找到最优解，都使用当前的解决方案
        self.best_schedule = solution

        if solution and len(solution.assignments) > 0:
            logging.info("成功找到监考安排方案")
        else:
            logging.warning("找到的监考安排方案可能不完整，请检查结果")

        return self.best_schedule

    def export_results(self, schedule: Schedule):
        """
        导出结果到Excel文件

        参数:
            schedule: 最终的调度方案
        """
        # 生成输出文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'监考安排_{timestamp}.xlsx'

        # 创建Excel写入器
        with pd.ExcelWriter(output_file) as writer:
            # 1. 导出考场安排表
            self._export_room_assignments(schedule, writer)

            # 2. 导出监考员安排表
            self._export_teacher_assignments(schedule, writer)

            # 3. 导出组时长分布数据
            if hasattr(self, 'scheduler') and self.scheduler:
                self.scheduler.export_group_duration_data(writer)

            # 4. 导出统计信息
            self._export_statistics(schedule, writer)

        logging.info(f"结果已导出到文件: {output_file}")

    def _export_room_assignments(self, schedule: Schedule, writer: pd.ExcelWriter):
        """导出考场安排表"""
        data = []
        for room in self.rooms:
            row = {'考场': room.name}
            for subject in self.original_subjects:
                key = (subject.name, room.name)
                teachers = schedule.assignments.get(key, [])
                required_count = room.subject_requirements.get(subject.name, 0)

                # 优先使用missing_teachers中的信息，如果没有则计算
                if key in schedule.missing_teachers:
                    missing_count = schedule.missing_teachers[key]
                else:
                    actual_count = len(teachers)
                    missing_count = required_count - actual_count

                if required_count > 0:
                    if teachers:
                        value = '|'.join(teachers)
                        if missing_count > 0:
                            value += f' 缺{missing_count}人'
                        row[subject.name] = value
                    elif missing_count > 0:
                        row[subject.name] = f'缺{missing_count}人'
                    else:
                        row[subject.name] = ''
                else:
                    row[subject.name] = ''
            data.append(row)

        df = pd.DataFrame(data)
        df.to_excel(writer, sheet_name='考场安排', index=False)

    def _export_teacher_assignments(self, schedule: Schedule, writer: pd.ExcelWriter):
        """导出监考员安排表"""
        data = []
        for teacher in self.teachers:
            assignments = schedule.teacher_assignments.get(teacher.name, [])

            # 计算总监考时长（小时）
            total_hours = 0
            for subject, _ in assignments:
                subject_obj = self.subject_map.get(subject)
                if subject_obj:
                    duration = (subject_obj.end_time - subject_obj.start_time).total_seconds() / 3600
                    total_hours += duration

            # 获取实际安排的场次数
            actual_sessions = len(assignments)

            # 如果教师对象有current_sessions属性，使用它
            if hasattr(teacher, 'current_sessions') and teacher.current_sessions is not None:
                actual_sessions = teacher.current_sessions

            # 创建基本行数据
            row = {
                '监考老师': teacher.name,
                '任教科目': teacher.teaching_subject,
                '场次限制': teacher.max_sessions,
                '实际安排次数': actual_sessions,
                '总监考时长(小时)': round(total_hours, 2),
                '安排状态': '已满足' if actual_sessions == teacher.max_sessions else f'差异: {teacher.max_sessions - actual_sessions}'
            }

            # 添加必监考考场满足情况
            if hasattr(teacher, 'satisfied_must_rooms'):
                row['必监考考场满足'] = ','.join(teacher.satisfied_must_rooms)
            else:
                row['必监考考场满足'] = ''

            # 添加不监考考场违反情况
            if hasattr(teacher, 'violated_forbidden_rooms'):
                row['不监考考场违反'] = ','.join(teacher.violated_forbidden_rooms)
            else:
                row['不监考考场违反'] = ''

            # 处理可能包含NaN的字段
            if teacher.must_subjects:
                row['必监考科目'] = ','.join(teacher.must_subjects)
            else:
                row['必监考科目'] = ''

            if teacher.forbidden_subjects:
                row['不监考科目'] = ','.join(teacher.forbidden_subjects)
            else:
                row['不监考科目'] = ''

            if teacher.must_rooms:
                row['必监考考场'] = ','.join(teacher.must_rooms)
            else:
                row['必监考考场'] = ''

            if teacher.forbidden_rooms:
                row['不监考考场'] = ','.join(teacher.forbidden_rooms)
            else:
                row['不监考考场'] = ''

            # 处理科目列
            for subject in self.original_subjects:
                rooms = [room for subj, room in assignments if subj == subject.name]
                if rooms:
                    row[subject.name] = ','.join(rooms)
                else:
                    row[subject.name] = ''

            data.append(row)

        # 创建DataFrame并确保所有值都是字符串类型，避免NaN
        df = pd.DataFrame(data)

        # 定义列顺序
        columns = [
            '监考老师', '任教科目', '必监考科目', '不监考科目', '必监考考场', '必监考考场满足',
            '不监考考场', '不监考考场违反', '场次限制', '实际安排次数', '总监考时长(小时)', '安排状态'
        ]
        columns.extend(subject.name for subject in self.original_subjects)

        # 按指定顺序排列列，并填充可能的NaN值
        df = df.reindex(columns=columns)

        # 全面处理NaN值，将所有NaN替换为空字符串
        df = df.astype(str).replace('nan', '')

        # 将DataFrame导出到Excel
        df.to_excel(writer, sheet_name='监考员安排', index=False)

    def _export_statistics(self, schedule: Schedule, writer: pd.ExcelWriter):
        """导出统计信息"""
        # 1. 计算总体统计信息
        total_teachers = len(self.teachers)
        total_subjects = len(self.subjects)
        total_rooms = len(self.rooms)

        # 计算每个科目的监考需求
        subject_requirements = {}
        for subject in self.subjects:
            total_required = sum(
                room.subject_requirements.get(subject.name, 0)
                for room in self.rooms
            )
            subject_requirements[subject.name] = total_required

        # 计算实际安排的监考人数和缺少的监考人数
        subject_actual = {}
        subject_missing = {}
        for subject in self.subjects:
            actual_count = 0
            missing_count = 0

            for room in self.rooms:
                key = (subject.name, room.name)
                # 计算实际安排的监考人数
                actual_count += len(schedule.assignments.get(key, []))
                # 计算缺少的监考人数
                if key in schedule.missing_teachers:
                    missing_count += schedule.missing_teachers[key]

            subject_actual[subject.name] = actual_count
            subject_missing[subject.name] = missing_count

        # 2. 计算教师安排情况
        teacher_stats = {
            '已满足场次限制': 0,
            '未满足场次限制': 0,
            '超出场次限制': 0
        }

        # 场次差异统计
        session_diff_stats = {0: 0, 1: 0, 2: 0, 'more': 0}

        # 如果Schedule对象有场次差异统计信息，使用它
        if hasattr(schedule, 'session_diff_stats') and schedule.session_diff_stats:
            session_diff_stats = schedule.session_diff_stats
        else:
            # 否则重新计算
            for teacher in self.teachers:
                # 获取实际安排的场次数
                actual_sessions = len(schedule.teacher_assignments.get(teacher.name, []))

                # 如果教师对象有current_sessions属性，使用它
                if hasattr(teacher, 'current_sessions') and teacher.current_sessions is not None:
                    actual_sessions = teacher.current_sessions

                # 计算差异
                diff = abs(actual_sessions - teacher.max_sessions)

                # 统计差异
                if diff <= 2:
                    session_diff_stats[diff] += 1
                else:
                    session_diff_stats['more'] += 1

        # 计算教师场次限制满足情况
        for teacher in self.teachers:
            # 获取实际安排的场次数
            actual_sessions = len(schedule.teacher_assignments.get(teacher.name, []))

            # 如果教师对象有current_sessions属性，使用它
            if hasattr(teacher, 'current_sessions') and teacher.current_sessions is not None:
                actual_sessions = teacher.current_sessions

            if actual_sessions == teacher.max_sessions:
                teacher_stats['已满足场次限制'] += 1
            elif actual_sessions < teacher.max_sessions:
                teacher_stats['未满足场次限制'] += 1
            else:
                teacher_stats['超出场次限制'] += 1

        # 3. 计算考场安排情况
        room_stats = {
            '已满足需求': 0,
            '未满足需求': 0,
            '超出需求': 0
        }

        for room in self.rooms:
            for subject_name, required in room.subject_requirements.items():
                key = (subject_name, room.name)
                actual = len(schedule.assignments.get(key, []))

                # 考虑缺少的监考教师
                missing = schedule.missing_teachers.get(key, 0)

                if missing > 0:
                    # 如果有缺少的监考教师，则未满足需求
                    room_stats['未满足需求'] += 1
                elif actual == required:
                    room_stats['已满足需求'] += 1
                elif actual < required:
                    room_stats['未满足需求'] += 1
                else:
                    room_stats['超出需求'] += 1

        # 4. 创建统计信息DataFrame
        stats_data = [
            {
                '统计项': '总体情况',
                '教师总数': total_teachers,
                '科目总数': total_subjects,
                '考场总数': total_rooms,
                '备注': ''
            },
            {
                '统计项': '教师安排情况',
                '已满足场次限制': teacher_stats['已满足场次限制'],
                '未满足场次限制': teacher_stats['未满足场次限制'],
                '超出场次限制': teacher_stats['超出场次限制'],
                '备注': ''
            },
            {
                '统计项': '场次差异统计',
                '无差异(0)': session_diff_stats[0],
                '差异为1': session_diff_stats[1],
                '差异为2': session_diff_stats[2],
                '差异更大': session_diff_stats['more'],
                '总体差异': getattr(schedule, 'total_session_diff', session_diff_stats[1] + 2*session_diff_stats[2] + 3*session_diff_stats['more']),
                '备注': '差异越小越好'
            },
            {
                '统计项': '必监考考场统计',
                '满足要求': getattr(schedule, 'must_room_stats', {}).get('满足', 0),
                '不满足要求': getattr(schedule, 'must_room_stats', {}).get('不满足', 0),
                '备注': '必监考考场要求已改为软约束'
            },
            {
                '统计项': '不监考考场统计',
                '满足要求': getattr(schedule, 'forbidden_room_stats', {}).get('满足', 0),
                '违反要求': getattr(schedule, 'forbidden_room_stats', {}).get('违反', 0),
                '备注': '不监考考场要求已改为软约束'
            },
            {
                '统计项': '考场安排情况',
                '已满足需求': room_stats['已满足需求'],
                '未满足需求': room_stats['未满足需求'],
                '超出需求': room_stats['超出需求'],
                '备注': ''
            }
        ]

        # 添加科目统计信息
        for subject in self.subjects:
            required = subject_requirements[subject.name]
            actual = subject_actual[subject.name]
            missing = subject_missing[subject.name]

            # 计算差异（考虑缺少的监考教师）
            diff = actual - required
            status = '已满足'

            if missing > 0:
                status = f'缺少{missing}人'
                diff = -missing
            elif diff < 0:
                status = '未满足'
            elif diff > 0:
                status = '超额安排'

            stats_data.append({
                '统计项': f'科目：{subject.name}',
                '需求人数': required,
                '实际安排': actual,
                '缺少人数': missing,
                '差异': diff,
                '备注': status
            })

        # 创建DataFrame并导出
        df = pd.DataFrame(stats_data)
        df.to_excel(writer, sheet_name='统计信息', index=False)

    def _is_time_overlap(self, slot1: Tuple[datetime, datetime],
                        slot2: Tuple[datetime, datetime]) -> bool:
        """检查两个时间段是否重叠"""
        start1, end1 = slot1
        start2, end2 = slot2
        return max(start1, start2) < min(end1, end2)

def main():
    """主函数"""
    try:
        # 初始化调度器，设置同组教师间监考时长最大差异为1.0小时
        scheduler = ExamScheduler("监考安排.xlsx", max_duration_diff=1.0)

        # 预处理
        scheduler.preprocess_teachers()
        scheduler.preprocess_subjects()

        # 执行监考安排
        best_schedule = scheduler.schedule_exams()

        # 导出结果
        scheduler.export_results(best_schedule)

    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()