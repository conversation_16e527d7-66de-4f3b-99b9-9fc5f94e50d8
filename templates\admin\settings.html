{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <h1 class="h2 mb-4">系统设置</h1>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    基本设置
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="max_file_size" class="form-label">最大文件大小（MB）</label>
                            <input type="number" class="form-control" id="max_file_size" name="max_file_size"
                                value="{{ (config['MAX_CONTENT_LENGTH'] / 1024 / 1024) | int }}" min="1" max="100" required>
                            <div class="form-text">上传文件的最大大小限制（1-100MB）</div>
                        </div>

                        <button type="submit" class="btn btn-primary">保存设置</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    系统信息
                </div>
                <div class="card-body">
                    <p><strong>系统版本：</strong> 1.0.0</p>
                    <p><strong>Python 版本：</strong> {{ sys.version.split()[0] }}</p>
                    <p><strong>Flask 版本：</strong> {{ flask_version }}</p>
                    <p><strong>数据库类型：</strong> SQLite</p>
                    <p><strong>上传目录：</strong> {{ config['UPLOAD_FOLDER'] }}</p>
                    <p><strong>允许的文件类型：</strong> {{ ', '.join(config['ALLOWED_EXTENSIONS']) }}</p>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    系统状态
                </div>
                <div class="card-body">
                    <p><strong>运行状态：</strong> <span class="badge bg-success">正常</span></p>
                    <p><strong>启动时间：</strong> {{ now.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                    <p><strong>总用户数：</strong> {{ User.query.count() }}</p>
                    <p><strong>总任务数：</strong> {{ Task.query.count() }}</p>
                    <p><strong>活跃任务：</strong> {{ Task.query.filter_by(status='processing').count() }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}