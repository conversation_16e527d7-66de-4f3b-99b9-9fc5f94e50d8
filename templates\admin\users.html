{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <h1 class="h2 mb-4">用户管理</h1>

    <div class="mb-4">
        <a href="{{ url_for('admin_user_add') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加用户
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th>任务限制</th>
                            <th>注册时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users.items %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.role == 'admin' %}
                                <span class="badge bg-danger">管理员</span>
                                {% elif user.role == 'vip' %}
                                <span class="badge bg-warning">VIP</span>
                                {% else %}
                                <span class="badge bg-secondary">普通用户</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_active %}
                                <span class="badge bg-success">活跃</span>
                                {% else %}
                                <span class="badge bg-danger">禁用</span>
                                {% endif %}
                            </td>
                            <td>{{ user.task_limit }}</td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <a href="{{ url_for('admin_user_edit', user_id=user.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                {% if user.id != current_user.id %}
                                <form action="{{ url_for('admin_user_delete', user_id=user.id) }}" method="POST" style="display: inline-block;" onsubmit="return confirm('确定要删除此用户吗？这将同时删除该用户的所有任务！');">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </form>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if users.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if users.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_users', page=users.prev_num) }}">上一页</a>
                    </li>
                    {% endif %}

                    {% for page in users.iter_pages() %}
                        {% if page %}
                            <li class="page-item {% if page == users.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('admin_users', page=page) }}">{{ page }}</a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_users', page=users.next_num) }}">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}