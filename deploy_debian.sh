#!/bin/bash
# 监考安排系统一键部署脚本 (Debian专用版)
# 作者：AI助手
# 日期：2024年4月20日

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$(id -u)" != "0" ]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用 'sudo $0' 重新运行"
        exit 1
    fi
}

# 创建应用目录
create_app_directory() {
    log_step "创建应用目录..."

    # 设置应用目录
    APP_DIR="/opt/jiankao"

    # 创建目录
    mkdir -p $APP_DIR

    # 复制当前目录下的所有文件到应用目录
    cp -r ./* $APP_DIR/

    # 设置权限
    chown -R www-data:www-data $APP_DIR

    log_info "应用目录创建完成: $APP_DIR"

    # 返回应用目录路径
    echo $APP_DIR
}

# 创建配置文件
create_config() {
    log_step "创建生产环境配置文件..."

    APP_DIR=$1

    if [ -f "$APP_DIR/config.py" ]; then
        log_warn "配置文件已存在，跳过创建"
        return
    fi

    # 生成随机密钥
    SECRET_KEY=$(python3 -c 'import secrets; print(secrets.token_hex(32))')

    cat > $APP_DIR/config.py << EOF
# 监考安排系统生产环境配置 (Debian)
# 自动生成于 $(date)

import os
import secrets

# 基本配置
SECRET_KEY = '$SECRET_KEY'
DEBUG = False
TESTING = False

# 数据库配置
SQLALCHEMY_DATABASE_URI = 'sqlite:///app.db'
SQLALCHEMY_TRACK_MODIFICATIONS = False

# 上传文件配置
UPLOAD_FOLDER = 'uploads'
MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {'xlsx'}

# 临时文件目录
TMP_DIR = '/tmp/jiankao'

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FILE = 'app.log'
LOG_DIR = 'logs'

# 安全配置
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
REMEMBER_COOKIE_SECURE = True
REMEMBER_COOKIE_HTTPONLY = True

# 缓存目录配置
CACHE_DIR = os.path.join(TMP_DIR, 'cache')

# 任务处理配置
DEFAULT_TIME_LIMIT = 300  # 默认求解时间限制(秒)
MAX_DURATION_DIFF = 1.0  # 同组教师间允许的最大监考时长差异(小时)

# Gunicorn配置
BIND = '0.0.0.0:5000'
WORKERS = 4
TIMEOUT = 120
ACCESS_LOG = os.path.join(LOG_DIR, 'gunicorn_access.log')
ERROR_LOG = os.path.join(LOG_DIR, 'gunicorn_error.log')
EOF

    log_info "配置文件已创建: $APP_DIR/config.py"
}

# 创建虚拟环境
create_virtual_env() {
    log_step "创建虚拟环境..."

    APP_DIR=$1

    if [ -d "$APP_DIR/venv" ]; then
        log_warn "虚拟环境已存在，跳过创建"
        return
    fi

    cd $APP_DIR
    python3 -m venv venv
    log_info "虚拟环境已创建"
}

# 安装依赖
install_dependencies() {
    log_step "安装依赖..."

    APP_DIR=$1

    cd $APP_DIR
    source venv/bin/activate

    # 升级pip
    pip install --upgrade pip

    # 安装生产环境依赖
    pip install -r requirements.txt

    # 安装额外的生产环境依赖
    pip install gunicorn

    log_info "依赖安装完成"
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."

    APP_DIR=$1

    cd $APP_DIR
    source venv/bin/activate

    # 创建数据库目录
    mkdir -p instance

    # 运行数据库初始化脚本
    python -c "
from app import app, db
from app import User
with app.app_context():
    db.create_all()
    # 检查管理员账号是否存在
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            task_limit=999
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print('管理员账号已创建')
    else:
        print('管理员账号已存在')
"

    log_info "数据库初始化完成"
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."

    APP_DIR=$1

    # 创建上传目录
    mkdir -p $APP_DIR/uploads

    # 创建日志目录
    mkdir -p $APP_DIR/logs

    # 创建模板目录
    mkdir -p $APP_DIR/template-guide

    # 创建临时目录
    mkdir -p /tmp/jiankao/cache
    mkdir -p /tmp/jiankao/tmp

    # 设置权限
    chmod -R 755 $APP_DIR/uploads
    chmod -R 755 $APP_DIR/logs
    chmod -R 777 /tmp/jiankao
    chown -R www-data:www-data $APP_DIR
    chown -R www-data:www-data /tmp/jiankao

    log_info "目录结构创建完成"
}

# 配置Supervisor
configure_supervisor() {
    log_step "配置Supervisor..."

    APP_DIR=$1

    # 创建Supervisor配置文件
    cat > /etc/supervisor/conf.d/jiankao.conf << EOF
[program:jiankao]
command=$APP_DIR/venv/bin/gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 120 --log-file=$APP_DIR/logs/gunicorn.log app:app
directory=$APP_DIR
user=www-data
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
stderr_logfile=$APP_DIR/logs/supervisor_err.log
stdout_logfile=$APP_DIR/logs/supervisor_out.log
environment=FLASK_APP=app.py,FLASK_ENV=production

[program:jiankao_cleanup]
command=/bin/bash -c "find /tmp/jiankao -type f -mtime +7 -delete"
user=www-data
autostart=true
autorestart=false
startsecs=0
startretries=0
exitcodes=0
stderr_logfile=$APP_DIR/logs/cleanup_err.log
stdout_logfile=$APP_DIR/logs/cleanup_out.log
schedule=0 3 * * *
EOF

    # 重新加载Supervisor配置
    supervisorctl reread
    supervisorctl update

    log_info "Supervisor配置完成"
}

# 配置Nginx
configure_nginx() {
    log_step "配置Nginx..."

    # 设置默认域名
    DEFAULT_DOMAIN="jiankao.her5.com"
    read -p "请确认您的域名 [默认: $DEFAULT_DOMAIN] (如果使用默认域名，请直接按回车，如果不使用域名，请输入 'no'): " DOMAIN_INPUT

    if [ -z "$DOMAIN_INPUT" ]; then
        # 使用默认域名
        DOMAIN_NAME="$DEFAULT_DOMAIN"
        SERVER_NAME="$DOMAIN_NAME"
        USE_SSL=true
        log_info "将使用默认域名: $DOMAIN_NAME"
    elif [ "$DOMAIN_INPUT" = "no" ]; then
        # 不使用域名
        SERVER_NAME="_"
        USE_SSL=false
        log_warn "将使用IP地址访问，不启用SSL"
    else
        # 使用用户输入的域名
        DOMAIN_NAME="$DOMAIN_INPUT"
        SERVER_NAME="$DOMAIN_NAME"
        USE_SSL=true
        log_info "将使用域名: $DOMAIN_NAME"
    fi

    # 创建Nginx配置文件
    cat > /etc/nginx/sites-available/jiankao << EOF
server {
    listen 80;
    server_name $SERVER_NAME;

    access_log /var/log/nginx/jiankao_access.log;
    error_log /var/log/nginx/jiankao_error.log;

    # 设置客户端上传文件大小限制
    client_max_body_size 10M;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /static {
        alias /opt/jiankao/static;
        expires 30d;
    }

    location /uploads {
        # 禁止直接访问上传目录
        deny all;
        return 404;
    }
}
EOF

    # 启用站点
    ln -sf /etc/nginx/sites-available/jiankao /etc/nginx/sites-enabled/

    # 测试Nginx配置
    nginx -t

    # 重启Nginx
    systemctl restart nginx

    log_info "Nginx基础配置完成"

    # 如果提供了域名，配置SSL
    if [ "$USE_SSL" = true ]; then
        configure_ssl "$DOMAIN_NAME"
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本..."

    APP_DIR=$1

    # 创建启动脚本
    cat > $APP_DIR/start.sh << EOF
#!/bin/bash
# 监考安排系统启动脚本
# 自动生成于 $(date)

# 启动服务
supervisorctl start jiankao
echo "监考安排系统已启动"
EOF

    # 创建停止脚本
    cat > $APP_DIR/stop.sh << EOF
#!/bin/bash
# 监考安排系统停止脚本
# 自动生成于 $(date)

# 停止服务
supervisorctl stop jiankao
echo "监考安排系统已停止"
EOF

    # 创建重启脚本
    cat > $APP_DIR/restart.sh << EOF
#!/bin/bash
# 监考安排系统重启脚本
# 自动生成于 $(date)

# 重启服务
supervisorctl restart jiankao
echo "监考安排系统已重启"
EOF

    # 创建状态检查脚本
    cat > $APP_DIR/status.sh << EOF
#!/bin/bash
# 监考安排系统状态检查脚本
# 自动生成于 $(date)

# 检查服务状态
supervisorctl status jiankao
EOF

    # 设置执行权限
    chmod +x $APP_DIR/start.sh
    chmod +x $APP_DIR/stop.sh
    chmod +x $APP_DIR/restart.sh
    chmod +x $APP_DIR/status.sh

    log_info "管理脚本已创建"
}

# 配置SSL证书
configure_ssl() {
    log_step "配置SSL证书..."

    DOMAIN_NAME=$1
    SERVER_IP="**************"

    # 检查域名是否有效
    if [ -z "$DOMAIN_NAME" ] || [ "$DOMAIN_NAME" = "_" ]; then
        log_error "无法为通配符域名配置SSL证书"
        return 1
    fi

    log_info "正在为域名 $DOMAIN_NAME 申请SSL证书..."
    log_info "服务器IP地址: $SERVER_IP"

    # 检查DNS解析
    log_step "检查DNS解析..."
    RESOLVED_IP=$(dig +short $DOMAIN_NAME A || host -t A $DOMAIN_NAME | grep "has address" | awk '{print $NF}')

    if [ -z "$RESOLVED_IP" ]; then
        log_error "DNS解析失败: 无法解析域名 $DOMAIN_NAME"
        log_warn "请确保DNS记录已正确设置并已生效"
        log_warn "系统将继续尝试申请SSL证书，但可能会失败"
    elif [ "$RESOLVED_IP" != "$SERVER_IP" ]; then
        log_warn "DNS解析不匹配: 域名 $DOMAIN_NAME 解析为 $RESOLVED_IP，但服务器IP是 $SERVER_IP"
        log_warn "请检查DNS记录是否正确"
        log_warn "系统将继续尝试申请SSL证书，但可能会失败"
    else
        log_info "DNS解析正确: $DOMAIN_NAME -> $RESOLVED_IP"
    fi

    # 使用Certbot自动申请证书并配置Nginx
    certbot --nginx -d $DOMAIN_NAME --non-interactive --agree-tos --email <EMAIL> --redirect

    if [ $? -eq 0 ]; then
        log_info "SSL证书配置成功！"

        # 设置自动续期
        log_info "配置证书自动续期..."
        systemctl enable certbot.timer
        systemctl start certbot.timer

        log_info "证书将自动续期"
    else
        log_error "SSL证书配置失败，请检查域名是否正确并确保DNS已正确设置"
        log_warn "系统将继续使用HTTP协议"
    fi
}

# 检查并安装缺失的依赖
check_and_install_dependency() {
    local package=$1
    log_step "检查并安装 $package..."

    if ! dpkg -l | grep -q "$package"; then
        log_info "$package 未安装，正在安装..."
        apt-get install -y "$package"
        log_info "$package 安装完成"
    else
        log_info "$package 已安装"
    fi
}

# 主函数
main() {
    log_info "开始部署监考安排系统到Debian环境..."

    # 检查root权限
    check_root

    # 更新软件包列表
    log_step "更新软件包列表..."
    apt-get update

    # 安装基本工具
    log_step "安装基本工具..."
    apt-get install -y curl wget gnupg2 ca-certificates lsb-release apt-transport-https

    # 按顺序安装依赖
    check_and_install_dependency "python3"
    check_and_install_dependency "python3-venv"
    check_and_install_dependency "python3-pip"

    # 创建应用目录
    APP_DIR=$(create_app_directory)

    # 创建目录结构
    create_directories "$APP_DIR"

    # 创建配置文件
    create_config "$APP_DIR"

    # 创建虚拟环境并安装Python依赖
    create_virtual_env "$APP_DIR"
    install_dependencies "$APP_DIR"

    # 初始化数据库
    init_database "$APP_DIR"

    # 安装和配置系统服务
    check_and_install_dependency "supervisor"
    configure_supervisor "$APP_DIR"

    check_and_install_dependency "nginx"
    configure_nginx

    # 如果需要SSL，安装certbot
    check_and_install_dependency "certbot"
    check_and_install_dependency "python3-certbot-nginx"

    # 创建管理脚本
    create_management_scripts "$APP_DIR"

    # 启动服务
    log_step "启动服务..."
    systemctl restart supervisor
    supervisorctl reread
    supervisorctl update
    supervisorctl start jiankao

    log_info "部署完成！"

    # 显示使用说明
    echo -e "${GREEN}使用说明:${NC}"
    echo -e "1. 系统已自动启动，可通过 http://服务器IP 或配置的域名访问"
    echo -e "2. 管理脚本位于 $APP_DIR 目录下:"
    echo -e "   - start.sh: 启动服务"
    echo -e "   - stop.sh: 停止服务"
    echo -e "   - restart.sh: 重启服务"
    echo -e "   - status.sh: 查看服务状态"
    echo -e "3. 默认管理员账号: admin / admin123"
    echo -e "4. 日志文件位于 $APP_DIR/logs 目录"
    echo -e "5. 临时文件位于 /tmp/jiankao 目录"
    echo -e "\n${RED}重要提示:${NC} 首次登录后请立即修改管理员密码！"
}

# 执行主函数
main
