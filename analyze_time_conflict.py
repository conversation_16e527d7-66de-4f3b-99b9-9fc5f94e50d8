#!/usr/bin/env python3
import pandas as pd

# 读取数据
result_file = '监考安排_20250602_160104.xlsx'
teacher_result = pd.read_excel(result_file, sheet_name='监考员安排')

print("=== 时间冲突导致缺1的根本原因分析 ===")

# 查看考试时间设置
print("1. 考试时间安排:")
print("  语文: 2025-05-28 18:10:00 - 2025-05-28 20:30:00")
print("  数学: 2025-05-29 07:30:00 - 2025-05-29 09:30:00")
print("  英语: 2025-05-29 10:00:00 - 2025-05-29 12:00:00")
print("  理化: 2025-05-29 13:40:00 - 2025-05-29 15:40:00")
print("  政史: 2025-05-29 16:00:00 - 2025-05-29 18:00:00")

# 分析有未安排场次的教师
print("\n2. 有未安排场次的教师详情:")
unassigned_teachers = teacher_result[teacher_result['未安排场次'] > 0]

for index, row in unassigned_teachers.iterrows():
    teacher_name = row['监考老师']
    print(f"\n  {teacher_name}:")
    print(f"    场次限制: {row['场次限制']}")
    print(f"    实际安排: {row['实际安排次数']}")
    print(f"    未安排: {row['未安排场次']}")
    
    # 查看已安排的科目
    arranged = []
    for subject in ['语文', '数学', '英语', '理化', '政史']:
        room = str(row.get(subject, ''))
        if room != 'nan' and room.strip() != '' and room != '0':
            arranged.append(f"{subject}({room})")
    
    print(f"    已安排科目: {', '.join(arranged) if arranged else '无'}")
    
    # 分析为什么没有安排政史
    politics_room = str(row.get('政史', ''))
    if politics_room == 'nan' or politics_room.strip() == '' or politics_room == '0':
        print(f"    未安排政史的可能原因:")
        
        # 检查约束条件
        forbidden_subjects = str(row.get('不监考科目', ''))
        must_rooms = str(row.get('必监考考场', ''))
        forbidden_rooms = str(row.get('不监考考场', ''))
        
        if '数学' in forbidden_subjects:
            print(f"      ❌ 禁止监考数学，可能影响整体安排")
        
        if must_rooms != 'nan' and must_rooms.strip() != '':
            print(f"      ⚠️ 必监考考场限制: {must_rooms}")
            print(f"      → 13、14、15考场可能不在必监考考场列表中")
        
        if forbidden_rooms != 'nan' and forbidden_rooms.strip() != '':
            print(f"      ⚠️ 不监考考场限制: {forbidden_rooms}")
            print(f"      → 13、14、15考场可能在不监考考场列表中")
        
        # 检查是否与其他时间段有冲突
        has_conflicting_subject = False
        for subject in ['数学', '英语', '理化']:  # 这些都在5月29日
            room = str(row.get(subject, ''))
            if room != 'nan' and room.strip() != '' and room != '0':
                has_conflicting_subject = True
                print(f"      ❌ 时间冲突: 已安排{subject}({room})，与政史时间可能有冲突")
        
        if not has_conflicting_subject:
            print(f"      ✅ 无明显时间冲突")
            print(f"      → 可能是考场约束或算法分配问题")

print("\n3. 关键发现:")
print("  刘雪珂、余小双、陈勇三位老师:")
print("  - 都只安排了1场监考(英语)")
print("  - 都还有1场未安排")
print("  - 政史科目正好缺3个考场(13、14、15考场)")
print("  - 英语时间: 10:00-12:00，政史时间: 16:00-18:00，无时间冲突")

print("\n4. 缺1的根本原因:")
print("  ❌ 不是时间冲突问题")
print("  ❌ 不是教师总量不足问题")
print("  ⚠️ 可能的原因:")
print("    1. 考场约束: 这3位教师可能有必监考考场或不监考考场的限制")
print("    2. 算法分配问题: 算法在处理约束时出现了次优解")
print("    3. 约束冲突: 多重约束条件相互冲突导致无法完美分配")

# 检查这3位教师的具体约束
print("\n5. 详细约束分析:")
problem_teachers = ['刘雪珂', '余小双', '陈勇']
for teacher_name in problem_teachers:
    teacher_row = teacher_result[teacher_result['监考老师'] == teacher_name]
    if not teacher_row.empty:
        row = teacher_row.iloc[0]
        print(f"\n  {teacher_name}:")
        print(f"    任教科目: {str(row.get('任教科目', ''))}")
        print(f"    必监考科目: {str(row.get('必监考科目', ''))}")
        print(f"    不监考科目: {str(row.get('不监考科目', ''))}")
        print(f"    必监考考场: {str(row.get('必监考考场', ''))}")
        print(f"    不监考考场: {str(row.get('不监考考场', ''))}")
        
        # 检查具体约束情况
        must_rooms = str(row.get('必监考考场', ''))
        forbidden_rooms = str(row.get('不监考考场', ''))
        
        if must_rooms != 'nan' and must_rooms.strip() != '':
            print(f"    ⚠️ 如果13/14/15考场不在必监考考场[{must_rooms}]中，就无法安排政史")
        
        if forbidden_rooms != 'nan' and forbidden_rooms.strip() != '':
            print(f"    ⚠️ 如果13/14/15考场在不监考考场[{forbidden_rooms}]中，就无法安排政史") 