<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>监考系统管理后台</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <link href="{{ url_for('static', filename='css/google-fonts.css') }}" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1976D2;
            --primary-light: #64B5F6;
            --accent-color: #FF5722;
            --background-color: #F5F7FA;
            --text-color: #333333;
            --text-secondary: #757575;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
            --sidebar-width: 220px;  /* 减小侧边栏宽度 */
        }

        /* 防止水平滚动 */
        html, body {
            overflow-x: hidden;
            width: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            transition: background-color var(--transition-speed);
            min-height: 100vh;
            position: relative;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 60px 0 0;
            width: var(--sidebar-width);
            background-color: white;
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed);
        }

        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 60px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }

        /* 导航栏样式 */
        .navbar {
            background-color: var(--primary-color) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            height: 60px;
            padding: 0 1rem;
            position: fixed;
            width: 100%;
            z-index: 1030;
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: 0.5px;
            font-size: 1rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 主内容区样式 */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 70px 15px 15px;
            min-height: calc(100vh - 60px);
            transition: all var(--transition-speed);
            width: calc(100% - var(--sidebar-width));
            max-width: 100%;
        }

        /* 导航项样式 */
        .nav-item {
            width: 100%;
        }

        .nav-link {
            padding: 0.8rem 1rem;
            color: var(--text-color);
            transition: all var(--transition-speed);
            border-radius: 6px;
            margin: 0 0.5rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .nav-link i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* 内容容器样式 */
        .container-fluid {
            padding: 0;
            margin: 0;
            width: 100%;
            max-width: none;
        }

        .row {
            margin: 0;
            width: 100%;
        }

        /* 卡片样式优化 */
        .card {
            margin-bottom: 1rem;
            border-radius: 8px;
            overflow: hidden;
            width: 100%;
        }

        /* 表格响应式处理 */
        .table-responsive {
            margin: 0;
            padding: 0;
            width: 100%;
        }

        .table {
            margin-bottom: 0;
            width: 100%;
        }

        /* 响应式布局优化 */
        @media (max-width: 768px) {
            :root {
                --sidebar-width: 200px;
            }

            .sidebar {
                margin-left: calc(var(--sidebar-width) * -1);
            }

            .sidebar.active {
                margin-left: 0;
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }

            .main-content.active {
                margin-left: var(--sidebar-width);
                width: calc(100% - var(--sidebar-width));
            }

            .navbar-brand {
                max-width: 200px;
            }
        }

        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            :root {
                --background-color: #121212;
                --text-color: #E0E0E0;
                --text-secondary: #AAAAAA;
                --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            }

            body {
                background-color: var(--background-color);
            }

            .sidebar, .card {
                background-color: #1E1E1E;
            }

            .nav-link {
                color: var(--text-color);
            }

            .nav-link:hover, .nav-link.active {
                background-color: var(--primary-color);
                color: white;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 滚动条样式优化 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="fade-in">
    <nav class="navbar navbar-dark">
        <div class="container-fluid px-3">
            <button id="sidebarToggle" class="btn btn-link text-white d-md-none p-0">
                <i class="fas fa-bars"></i>
            </button>
            <a class="navbar-brand m-0" href="{{ url_for('admin_dashboard') }}">
                <i class="fas fa-shield-alt me-2"></i>监考系统管理后台
            </a>
            <ul class="navbar-nav">
                <li class="nav-item text-nowrap">
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt"></i>
                        <span class="d-none d-sm-inline">退出登录</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-2 d-md-block sidebar">
                <div class="sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                                <i class="fas fa-home"></i>仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_users') }}">
                                <i class="fas fa-users"></i>用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_tasks') }}">
                                <i class="fas fa-tasks"></i>任务管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_settings') }}">
                                <i class="fas fa-cog"></i>系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main role="main" class="main-content">
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-info alert-dismissible fade show shadow-sm" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script>
        // 侧边栏切换功能
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('active');
            document.querySelector('.main-content').classList.toggle('active');
        });

        // 自动设置当前活动导航项
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });

            // 自动为所有AJAX请求添加CSRF令牌
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const csrfValue = csrfToken.getAttribute('content');

                // 为jQuery AJAX请求添加CSRF令牌
                if (typeof $ !== 'undefined') {
                    $.ajaxSetup({
                        beforeSend: function(xhr, settings) {
                            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                                xhr.setRequestHeader("X-CSRFToken", csrfValue);
                            }
                        }
                    });
                }

                // 为Fetch API添加CSRF令牌
                const originalFetch = window.fetch;
                window.fetch = function(url, options) {
                    options = options || {};
                    if (!options.headers) {
                        options.headers = {};
                    }

                    if (options.method && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(options.method) && !url.includes('http')) {
                        options.headers['X-CSRFToken'] = csrfValue;
                    }

                    return originalFetch(url, options);
                };
            }
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>