#!/usr/bin/env python
# -*- coding: utf-8 -*-

from excel_validator import ExcelValidator
import pandas as pd
import numpy as np
from typing import List, Dict, Any
import logging
from datetime import datetime
import re

logger = logging.getLogger('jiankao_app.extended_validator')

class ExtendedExcelValidator(ExcelValidator):
    """扩展的Excel验证器，增加了更多的验证规则"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.strict_validation_failed = False  # 严格验证是否失败
        self.warnings = []  # 警告信息列表

    def validate(self) -> bool:
        """
        执行所有验证步骤，包括基础验证和扩展验证

        返回:
            bool: 验证是否通过
        """
        try:
            # 更新进度：开始验证
            self._update_progress(0, "开始验证...")

            # 首先执行基础验证
            self._update_progress(10, "执行基础验证...")
            if not super().validate():
                self.is_valid = False
                self._update_progress(100, "基础验证失败")
                return False
            self._update_progress(30, "基础验证完成")

            # 执行严格验证
            self._update_progress(40, "执行严格验证规则...")
            self._validate_strict_rules()
            self._update_progress(70, "严格验证完成")
            
            # 执行警告级别的验证
            self._update_progress(80, "执行警告级别验证...")
            self._validate_warning_rules()
            self._update_progress(90, "警告验证完成")

            # 更新最终验证结果
            self.is_valid = not self.strict_validation_failed
            
            # 如果验证失败，生成错误报告
            if not self.is_valid:
                error_report_path = self._generate_error_report()
                if error_report_path:
                    self.validation_messages['error_report'] = error_report_path

            self._update_progress(100, "验证完成")
            return self.is_valid

        except Exception as e:
            logger.error(f"扩展验证过程发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"验证过程发生错误: {str(e)}")
            self.is_valid = False
            self._update_progress(100, "验证失败")
            return False

    def _validate_strict_rules(self) -> None:
        """执行严格验证规则"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            self._update_progress(45, "读取监考员数据...")
            
            # 1. 监考员基本信息检查
            self._update_progress(50, "检查监考员基本信息...")
            self._validate_teacher_basic_info(teacher_df)
            
            # 2. 场次限制有效性检查
            self._update_progress(55, "检查场次限制...")
            self._validate_session_limits(teacher_df)
            
            # 3. 数据格式规范检查
            self._update_progress(60, "检查数据格式...")
            self._validate_data_format(teacher_df)

            # 如果没有错误，确保strict_validation_failed为False
            if len(self.errors) == 0:
                self.strict_validation_failed = False
                logger.info("严格验证通过")
                self._update_progress(65, "严格验证规则通过")

        except Exception as e:
            logger.error(f"执行严格验证规则时发生错误: {str(e)}", exc_info=True)
            self.strict_validation_failed = True
            self.errors.append(f"严格验证失败: {str(e)}")
            self._update_progress(65, "严格验证规则出错")

    def _validate_warning_rules(self) -> None:
        """执行警告级别的验证规则"""
        try:
            # 1. 检查监考员任教科目与考试科目的匹配度
            self._check_subject_matching()
            
            # 2. 检查考试时间分布是否合理
            self._check_exam_time_distribution()
            
            # 3. 检查监考员工作量分布
            self._check_workload_distribution()

        except Exception as e:
            logger.warning(f"执行警告级别验证时发生错误: {str(e)}", exc_info=True)
            self.warnings.append(f"警告级别验证过程出现异常: {str(e)}")

    def _check_subject_matching(self) -> None:
        """检查监考员任教科目与考试科目的匹配度"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')

            # 获取所有考试科目
            exam_subjects = set(subject_df['课程名称'].unique())
            
            # 收集所有不匹配的监考员信息
            unmatch_teachers = []
            
            # 检查每个监考员的任教科目
            for _, row in teacher_df.iterrows():
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    # 只有当任教科目不为'0'时才进行匹配检查
                    if subject_str != '0':
                        teacher_subjects = {s.strip() for s in subject_str.split(',') if s.strip()}
                        
                        # 如果监考员没有任教考试科目中的任何一门
                        if not teacher_subjects & exam_subjects:
                            unmatch_teachers.append({
                                'name': row['监考老师'],
                                'subjects': ','.join(teacher_subjects)
                            })
            
            # 如果存在不匹配的监考员，生成汇总警告信息
            if unmatch_teachers:
                # 按照教师姓名排序
                unmatch_teachers.sort(key=lambda x: x['name'])
                
                # 生成汇总信息
                teacher_count = len(unmatch_teachers)
                teacher_list = '\n'.join([
                    f"- {t['name']}（任教科目：{t['subjects']}）" 
                    for t in unmatch_teachers
                ])
                
                warning_msg = (
                    f"发现{teacher_count}位监考员的任教科目与考试科目无重叠，可能不熟悉考试内容：\n"
                    f"{teacher_list}\n"
                    f"建议检查这些监考员的安排是否合理。"
                )
                
                self.add_warning(warning_msg)

        except Exception as e:
            logger.warning(f"检查科目匹配度时发生错误: {str(e)}", exc_info=True)

    def _check_exam_time_distribution(self) -> None:
        """检查考试时间分布是否合理"""
        try:
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
            
            # 将时间字符串转换为datetime
            subject_df['开始时间'] = pd.to_datetime(subject_df['开始时间'], format='%H:%M').dt.time
            subject_df['结束时间'] = pd.to_datetime(subject_df['结束时间'], format='%H:%M').dt.time
            
            # 检查是否存在考试时间过于集中的情况
            time_slots = []
            for _, row in subject_df.iterrows():
                time_slots.append((row['开始时间'], row['结束时间']))
            
            # 统计同时进行的考试数量
            max_concurrent = 0
            for i, (start1, end1) in enumerate(time_slots):
                concurrent = 1
                for j, (start2, end2) in enumerate(time_slots):
                    if i != j:
                        if (start1 <= start2 < end1) or (start2 <= start1 < end2):
                            concurrent += 1
                max_concurrent = max(max_concurrent, concurrent)
            
            if max_concurrent > len(subject_df) / 2:
                self.add_warning(
                    f"考试时间分布过于集中，最多有{max_concurrent}场考试同时进行，建议适当分散考试时间"
                )

        except Exception as e:
            logger.warning(f"检查考试时间分布时发生错误: {str(e)}", exc_info=True)

    def _check_workload_distribution(self) -> None:
        """检查监考员工作量分布是否合理"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            
            # 计算平均场次限制
            avg_limit = teacher_df['场次限制'].mean()
            std_limit = teacher_df['场次限制'].std()
            
            # 检查场次限制差异过大的情况
            for _, row in teacher_df.iterrows():
                if abs(row['场次限制'] - avg_limit) > 2 * std_limit:
                    self.add_warning(
                        f"监考员 {row['监考老师']} 的场次限制({row['场次限制']})与平均值({avg_limit:.1f})差异较大，" +
                        "建议适当调整以保证工作量均衡"
                    )

        except Exception as e:
            logger.warning(f"检查工作量分布时发生错误: {str(e)}", exc_info=True)

    def _validate_teacher_basic_info(self, teacher_df: pd.DataFrame) -> None:
        """验证监考员基本信息的完整性和有效性"""
        try:
            # 检查必要列是否存在
            required_columns = ['序号', '监考老师', '任教科目', '场次限制', '必监考科目', '不监考科目']
            missing_columns = [col for col in required_columns if col not in teacher_df.columns]
            if missing_columns:
                error_msg = f"监考员设置表缺少必要的列: {', '.join(missing_columns)}"
                self.errors.append(error_msg)
                self.add_error_detail('监考员设置', 1, '列名', '', '缺少列', error_msg)
                self.strict_validation_failed = True
                return

            # 检查是否有重复的监考员
            duplicates = teacher_df[teacher_df['监考老师'].duplicated()]['监考老师'].tolist()
            if duplicates:
                error_msg = f"存在重复的监考员: {', '.join(duplicates)}"
                self.errors.append(error_msg)
                self.add_error_detail('监考员设置', 1, '监考老师', str(duplicates), '数据重复', error_msg)
                self.strict_validation_failed = True

            # 检查监考老师姓名的有效性
            invalid_names = teacher_df[
                ~teacher_df['监考老师'].astype(str).str.match(r'^[\u4e00-\u9fa5]{2,4}$')
            ]['监考老师'].tolist()
            if invalid_names:
                error_msg = f"监考老师姓名格式无效: {', '.join(map(str, invalid_names))}"
                self.errors.append(error_msg)
                self.add_error_detail('监考员设置', 1, '监考老师', str(invalid_names), '格式错误', error_msg)
                self.strict_validation_failed = True

            # 检查必监考科目和不监考科目的重复值
            for idx, row in teacher_df.iterrows():
                # 处理必监考科目
                must_subjects = []
                if pd.notna(row['必监考科目']):
                    must_str = str(row['必监考科目'])
                    if must_str != '0' and must_str.strip():  # 如果不是0且不是空白
                        must_subjects = [s.strip() for s in must_str.split(',') if s.strip()]

                # 处理不监考科目
                avoid_subjects = []
                if pd.notna(row['不监考科目']):
                    avoid_str = str(row['不监考科目'])
                    if avoid_str != '0' and avoid_str.strip():  # 如果不是0且不是空白
                        avoid_subjects = [s.strip() for s in avoid_str.split(',') if s.strip()]

                # 只有当两个列表都不为空时才检查重复
                if must_subjects and avoid_subjects:
                    # 检查重复值
                    common_subjects = set(must_subjects) & set(avoid_subjects)
                    if common_subjects:
                        error_msg = f"监考员设置表第{idx+1}行（{row['监考老师']}）的必监考科目和不监考科目存在重复: {', '.join(common_subjects)}"
                        self.errors.append(error_msg)
                        self.add_error_detail('监考员设置', idx+1, '监考科目', str(common_subjects), '科目重复', error_msg)
                        self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证监考员基本信息时发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"验证监考员基本信息失败: {str(e)}")
            self.strict_validation_failed = True

    def _validate_session_limits(self, teacher_df: pd.DataFrame) -> None:
        """验证场次限制的有效性"""
        # 检查场次限制是否为正整数
        invalid_limits = teacher_df[~teacher_df['场次限制'].apply(lambda x: isinstance(x, (int, float)) and x > 0)]
        if not invalid_limits.empty:
            rows = invalid_limits.index.tolist()
            error_msg = f"以下行的场次限制必须为正整数: {[r+2 for r in rows]}"
            self.add_error_detail('监考员设置', rows[0]+2, '场次限制', 
                                str(invalid_limits.iloc[0]['场次限制']), '数据格式错误', error_msg)
            self.strict_validation_failed = True

        # 检查场次限制是否为0
        zero_limits = teacher_df[teacher_df['场次限制'] == 0]
        if not zero_limits.empty:
            rows = zero_limits.index.tolist()
            error_msg = f"以下行的场次限制不能为0: {[r+2 for r in rows]}"
            self.add_error_detail('监考员设置', rows[0]+2, '场次限制', '0', '数据值错误', error_msg)
            self.strict_validation_failed = True

        # 检查场次限制总和是否大于等于考试科目总场次
        subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
        total_sessions = len(subject_df)
        total_limits = teacher_df['场次限制'].sum()
        if total_limits < total_sessions:
            error_msg = f"监考员场次限制总和({total_limits})必须大于等于考试总场次数({total_sessions})"
            self.add_error_detail('监考员设置', 2, '场次限制', str(total_limits), '数据值错误', error_msg)
            self.strict_validation_failed = True

    def _validate_data_format(self, teacher_df: pd.DataFrame) -> None:
        """验证数据格式的规范性"""
        try:
            # 检查序号的连续性
            expected_seq = range(1, len(teacher_df) + 1)
            actual_seq = teacher_df['序号'].tolist()
            if not all(a == e for a, e in zip(actual_seq, expected_seq)):
                error_msg = "序号不连续或不从1开始"
                self.errors.append(error_msg)
                self.add_error_detail('监考员设置', 1, '序号', str(actual_seq), '序号错误', error_msg)
                self.strict_validation_failed = True

            # 检查任教科目格式
            for idx, row in teacher_df.iterrows():
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    # 如果值不是'0'且不是'nan'，则进行格式验证
                    if subject_str != '0' and subject_str.lower() != 'nan':
                        if not bool(re.match(r'^[\u4e00-\u9fa5,]+$', subject_str)):
                            error_msg = f"监考员设置表第{idx+1}行（{row['监考老师']}）的任教科目格式无效（应为中文科目名，多个科目用逗号分隔）"
                            self.errors.append(error_msg)
                            self.add_error_detail('监考员设置', idx+1, '任教科目', subject_str, '格式错误', error_msg)
                            self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证数据格式时发生错误: {str(e)}", exc_info=True)
            self.strict_validation_failed = True
            self.errors.append(f"验证数据格式时发生错误: {str(e)}")

    def _validate_subject_sheet(self):
        sheet = pd.read_excel(self.workbook, sheet_name='考试科目设置')
        
        # 检查必要的列
        required_columns = ['课程代码', '课程名称', '开始时间', '结束时间']
        if not all(col in sheet.columns for col in required_columns):
            missing_columns = [col for col in required_columns if col not in sheet.columns]
            self.errors.append(f"考试科目设置表缺少必要的列: {', '.join(missing_columns)}")
            return

        # 继续检查其他错误
        for idx, row in sheet.iterrows():
            # 检查空值
            if any(pd.isna(row[col]) for col in required_columns):
                self.errors.append(f"考试科目设置表第{idx+1}行存在空值")
                continue
            
            # 检查时间格式和逻辑
            try:
                start_time = row['开始时间']
                end_time = row['结束时间']
                if isinstance(start_time, str):
                    start_time = datetime.strptime(start_time, '%H:%M').time()
                if isinstance(end_time, str):
                    end_time = datetime.strptime(end_time, '%H:%M').time()
                
                if end_time <= start_time:
                    self.errors.append(f"考试科目设置表第{idx+1}行结束时间不能早于或等于开始时间")
            except ValueError:
                self.errors.append(f"考试科目设置表第{idx+1}行时间格式错误，应为HH:MM格式")

        # 检查考试时间重合
        exam_times = []
        for idx, row in sheet.iterrows():
            try:
                start_time = row['开始时间']
                end_time = row['结束时间']
                if isinstance(start_time, str):
                    start_time = datetime.strptime(start_time, '%H:%M').time()
                if isinstance(end_time, str):
                    end_time = datetime.strptime(end_time, '%H:%M').time()
                
                # 检查与之前的考试时间是否重合
                for prev_start, prev_end, prev_idx in exam_times:
                    if (start_time < prev_end and end_time > prev_start):
                        error_msg = f"考试科目设置表第{idx+1}行的考试时间与第{prev_idx+1}行时间重合"
                        self.errors.append(error_msg)
                        self.add_error_detail('考试科目设置', idx+1, '考试时间', f"{start_time}-{end_time}", '时间重合', error_msg)
                
                exam_times.append((start_time, end_time, idx))
            except (ValueError, TypeError):
                continue

    def get_validation_messages(self) -> Dict[str, Any]:
        """
        获取验证消息，包括错误、警告和错误报告路径

        返回:
            Dict: 包含验证结果的字典
        """
        messages = super().get_validation_messages()
        if hasattr(self, 'validation_messages') and 'error_report' in self.validation_messages:
            messages['error_report'] = self.validation_messages['error_report']
        return messages 