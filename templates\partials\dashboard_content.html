<!-- Dashboard Header -->
<div class="dashboard-header">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h2 class="mb-0 fw-bold">控制台</h2>
            <p class="text-muted mb-0">欢迎回来，{{ current_user.username }}</p>
            {% if current_user.role != 'admin' %}
            <p class="text-muted mt-2">
                <i class="fas fa-info-circle me-1"></i>
                您还可以创建 <span class="badge bg-primary">{{ remaining_tasks }}</span> 个任务
                {% if remaining_tasks == 0 %}
                <br>
                <small class="text-muted">
                    <i class="fas fa-lightbulb me-1"></i>
                    提示：您可以删除已完成或失败的任务来释放任务配额
                </small>
                {% endif %}
            </p>
            {% endif %}
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="{{ url_for('validate_test') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-check-circle me-2"></i> 验证文件
            </a>
            {% if current_user.role != 'admin' and current_user.tasks.count() >= current_user.task_limit %}
            <button class="btn btn-primary" disabled
                    data-bs-toggle="tooltip" 
                    data-bs-placement="bottom" 
                    title="您已达到任务数量限制，请删除已完成或失败的任务后再创建新任务">
                <i class="fas fa-plus-circle me-2"></i> 新建任务
            </button>
            {% else %}
            <a href="{{ url_for('new_task') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-2"></i> 新建任务
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Dashboard Stats -->
<div class="dashboard-stats">
    <div class="stat-card">
        <div class="stat-icon stat-pending">
            <i class="fas fa-hourglass-start"></i>
        </div>
        <h3 class="stat-value">{{ tasks|selectattr('status', 'equalto', 'pending')|list|length }}</h3>
        <p class="stat-label">等待处理</p>
    </div>

    <div class="stat-card">
        <div class="stat-icon stat-processing">
            <i class="fas fa-cog fa-spin"></i>
        </div>
        <h3 class="stat-value">{{ tasks|selectattr('status', 'equalto', 'processing')|list|length }}</h3>
        <p class="stat-label">处理中</p>
    </div>

    <div class="stat-card">
        <div class="stat-icon stat-completed">
            <i class="fas fa-check-circle"></i>
        </div>
        <h3 class="stat-value">{{ tasks|selectattr('status', 'equalto', 'completed')|list|length }}</h3>
        <p class="stat-label">已完成</p>
    </div>

    <div class="stat-card">
        <div class="stat-icon stat-failed">
            <i class="fas fa-exclamation-circle"></i>
        </div>
        <h3 class="stat-value">{{ tasks|selectattr('status', 'equalto', 'failed')|list|length }}</h3>
        <p class="stat-label">失败</p>
    </div>
</div>

<!-- Task List -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">我的任务</h4>

        <!-- Task Filters -->
        <div class="task-filter">
            <span class="filter-label">筛选：</span>
            <button class="filter-btn active" data-filter="all">全部</button>
            <button class="filter-btn" data-filter="pending">等待处理</button>
            <button class="filter-btn" data-filter="processing">处理中</button>
            <button class="filter-btn" data-filter="completed">已完成</button>
            <button class="filter-btn" data-filter="failed">失败</button>
        </div>
    </div>
</div>

{% if tasks %}
<div class="row" id="taskContainer">
    {% for task in tasks %}
    <div class="col-md-6 col-lg-4 task-item mb-4" data-status="{{ task.status }}">
        <div class="card task-card h-100">
            <!-- 任务头部 -->
            <div class="card-header bg-transparent border-bottom-0 d-flex justify-content-between align-items-center py-3">
                <h5 class="card-title mb-0 text-truncate" style="max-width: 70%;">{{ task.title }}</h5>
                <span class="badge bg-{{ task.status_color }} rounded-pill">{{ task.status_display }}</span>
            </div>
            
            <!-- 任务内容 -->
            <div class="card-body pb-2">
                {% if task.description %}
                <p class="card-text text-muted small mb-3">{{ task.description|truncate(100) }}</p>
                {% endif %}
                
                <!-- 进度条 -->
                <div class="progress mb-3" style="height: 6px;">
                    {% if task.status == 'processing' %}
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" 
                         style="width: {{ task.progress }}%" 
                         aria-valuenow="{{ task.progress }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                    {% elif task.status == 'completed' %}
                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                    {% elif task.status == 'failed' %}
                    <div class="progress-bar bg-danger" role="progressbar" style="width: 100%"></div>
                    {% else %}
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    {% endif %}
                </div>

                <!-- 时间信息 -->
                <div class="d-flex align-items-center text-muted small mb-3">
                    <div class="me-3">
                        <i class="far fa-calendar-alt me-1"></i>
                        {{ task.created_at.strftime('%Y-%m-%d') }}
                    </div>
                    <div>
                        <i class="far fa-clock me-1"></i>
                        {{ task.created_at.strftime('%H:%M') }}
                    </div>
                </div>
            </div>

            <!-- 任务操作按钮 -->
            <div class="card-footer bg-transparent border-top d-flex justify-content-end gap-2 py-2">
                {% if task.status in ['completed', 'failed'] or current_user.role == 'admin' %}
                <button onclick="confirmDeleteTask({{ task.id }})" 
                        class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-trash-alt me-1"></i>删除任务
                </button>
                {% endif %}
                <a href="{{ url_for('task_detail', task_id=task.id) }}" 
                   class="btn btn-primary btn-sm">
                    <i class="fas fa-eye me-1"></i>详情
                </a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="empty-state">
    <div class="empty-state-icon">
        <i class="fas fa-tasks"></i>
    </div>
    <h4 class="text-muted mb-3">您还没有创建任何任务</h4>
    <p class="text-muted mb-4">点击下面的按钮创建您的第一个监考安排任务</p>
    <a href="{{ url_for('new_task') }}" class="btn btn-primary btn-lg" {% if remaining_tasks == 0 %}disabled{% endif %}>
        <i class="fas fa-plus-circle me-2"></i> 创建第一个任务
    </a>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>删除任务后将释放任务配额，您可以创建新的任务。但任务相关的所有数据都将被永久删除，确定要继续吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- Add this script at the end of the file -->
<script>
let deleteTaskModal;
let taskIdToDelete;

document.addEventListener('DOMContentLoaded', function() {
    deleteTaskModal = new bootstrap.Modal(document.getElementById('deleteTaskModal'));
});

function confirmDeleteTask(taskId) {
    taskIdToDelete = taskId;
    deleteTaskModal.show();
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (!taskIdToDelete) return;
    
    // 发送删除请求
    fetch(`/task/${taskIdToDelete}/delete`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 删除成功，刷新页面
            location.reload();
        } else {
            // 显示错误消息
            alert(data.message || '删除失败，请重试');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('删除失败，请重试');
    })
    .finally(() => {
        deleteTaskModal.hide();
        taskIdToDelete = null;
    });
});
</script> 